#!/usr/bin/env python3
"""
Network Configuration Script
Configure network settings for ZK biometric device communication
Now supports both Ethernet and Cloud connectivity setup
"""

import subprocess
import sys
import platform
import logging
import json
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import cloud modules if available
try:
    from cloud_config import CloudConfigManager, CloudConfig, DeviceConfig
    CLOUD_ENABLED = True
except ImportError:
    logger.warning("Cloud modules not available. Cloud configuration will be skipped.")
    CLOUD_ENABLED = False

class NetworkConfig:
    """Network configuration handler"""
    
    def __init__(self):
        self.os_type = platform.system().lower()
    
    def configure_windows_network(self, interface_name: str = "Ethernet", 
                                ip_address: str = "*************",
                                subnet_mask: str = "*************",
                                gateway: str = "***********"):
        """Configure network settings on Windows"""
        try:
            # Set static IP address
            cmd_ip = [
                "netsh", "interface", "ip", "set", "address",
                f"name={interface_name}",
                "static",
                ip_address,
                subnet_mask,
                gateway
            ]
            
            logger.info(f"Setting IP address to {ip_address}")
            result = subprocess.run(cmd_ip, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("IP address configured successfully")
                return True
            else:
                logger.error(f"Failed to set IP address: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error configuring Windows network: {str(e)}")
            return False
    
    def configure_linux_network(self, interface_name: str = "eth0",
                               ip_address: str = "*************",
                               subnet_mask: str = "*************", 
                               gateway: str = "***********"):
        """Configure network settings on Linux"""
        try:
            # Calculate CIDR notation
            cidr = self._subnet_to_cidr(subnet_mask)
            
            # Set IP address
            cmd_ip = ["sudo", "ip", "addr", "add", f"{ip_address}/{cidr}", "dev", interface_name]
            result = subprocess.run(cmd_ip, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to set IP address: {result.stderr}")
                return False
            
            # Set gateway
            cmd_gateway = ["sudo", "ip", "route", "add", "default", "via", gateway]
            result = subprocess.run(cmd_gateway, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.warning(f"Failed to set gateway (may already exist): {result.stderr}")
            
            logger.info(f"Network configured: {ip_address}/{cidr} via {gateway}")
            return True
            
        except Exception as e:
            logger.error(f"Error configuring Linux network: {str(e)}")
            return False
    
    def _subnet_to_cidr(self, subnet_mask: str) -> int:
        """Convert subnet mask to CIDR notation"""
        cidr_map = {
            "*************": 24,
            "***********": 16,
            "*********": 8,
            "***************": 25,
            "***************": 26,
            "***************": 27,
            "***************": 28,
            "***************": 29,
            "***************": 30
        }
        return cidr_map.get(subnet_mask, 24)
    
    def get_network_interfaces(self) -> list:
        """Get available network interfaces"""
        try:
            if self.os_type == "windows":
                result = subprocess.run(
                    ["netsh", "interface", "show", "interface"],
                    capture_output=True, text=True, shell=True
                )
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    interfaces = []
                    for line in lines[3:]:  # Skip header lines
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 4:
                                interfaces.append(parts[-1])
                    return interfaces
            else:
                result = subprocess.run(["ip", "link", "show"], capture_output=True, text=True)
                if result.returncode == 0:
                    interfaces = []
                    for line in result.stdout.split('\n'):
                        if ': ' in line and 'state' in line:
                            interface = line.split(':')[1].strip()
                            interfaces.append(interface)
                    return interfaces
        except Exception as e:
            logger.error(f"Error getting network interfaces: {str(e)}")
        
        return []
    
    def ping_device(self, ip_address: str = "*************") -> bool:
        """Test connectivity to ZK device"""
        try:
            if self.os_type == "windows":
                cmd = ["ping", "-n", "4", ip_address]
            else:
                cmd = ["ping", "-c", "4", ip_address]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Successfully pinged {ip_address}")
                return True
            else:
                logger.error(f"Failed to ping {ip_address}")
                return False
                
        except Exception as e:
            logger.error(f"Error pinging device: {str(e)}")
            return False
    
    def configure_network(self, interface_name: str = None,
                         ip_address: str = "*************",
                         subnet_mask: str = "*************",
                         gateway: str = "***********") -> bool:
        """Configure network based on OS"""
        
        # Get available interfaces if not specified
        if not interface_name:
            interfaces = self.get_network_interfaces()
            if not interfaces:
                logger.error("No network interfaces found")
                return False
            
            # Use first available interface
            interface_name = interfaces[0]
            logger.info(f"Using interface: {interface_name}")
        
        # Configure based on OS
        if self.os_type == "windows":
            return self.configure_windows_network(interface_name, ip_address, subnet_mask, gateway)
        else:
            return self.configure_linux_network(interface_name, ip_address, subnet_mask, gateway)

def setup_cloud_config():
    """Setup cloud configuration"""
    if not CLOUD_ENABLED:
        print("❌ Cloud modules not available. Please install cloud dependencies.")
        return False

    print("\n🌐 Cloud Configuration Setup")
    print("=" * 40)

    config_manager = CloudConfigManager()

    # Get cloud configuration
    print("Enter cloud service details:")
    api_base_url = input("API Base URL (https://api.zkcloud.example.com): ").strip()
    if not api_base_url:
        api_base_url = "https://api.zkcloud.example.com"

    websocket_url = input("WebSocket URL (wss://ws.zkcloud.example.com): ").strip()
    if not websocket_url:
        websocket_url = "wss://ws.zkcloud.example.com"

    api_key = input("API Key: ").strip()
    if not api_key:
        print("❌ API Key is required for cloud connectivity")
        return False

    secret_key = input("Secret Key (optional): ").strip()
    organization_id = input("Organization ID: ").strip()
    if not organization_id:
        print("❌ Organization ID is required")
        return False

    # Update configuration
    config = config_manager.config
    config.api_base_url = api_base_url
    config.websocket_url = websocket_url
    config.api_key = api_key
    config.secret_key = secret_key
    config.organization_id = organization_id

    # Optional settings
    print("\nOptional settings (press Enter for defaults):")
    sync_interval = input(f"Sync interval in seconds ({config.sync_interval}): ").strip()
    if sync_interval:
        try:
            config.sync_interval = int(sync_interval)
        except ValueError:
            print("Invalid sync interval, using default")

    use_ssl = input(f"Use SSL (y/n, default: {'y' if config.use_ssl else 'n'}): ").strip().lower()
    if use_ssl in ['y', 'yes']:
        config.use_ssl = True
    elif use_ssl in ['n', 'no']:
        config.use_ssl = False

    # Save configuration
    config_manager.save_config()
    print("✅ Cloud configuration saved successfully!")

    return True

def setup_device_config():
    """Setup device configuration for cloud connectivity"""
    if not CLOUD_ENABLED:
        return False

    print("\n📱 Device Configuration Setup")
    print("=" * 40)

    config_manager = CloudConfigManager()

    # Get device details
    device_id = input("Device ID (ZK_001): ").strip()
    if not device_id:
        device_id = "ZK_001"

    device_name = input("Device Name (Main Biometric Device): ").strip()
    if not device_name:
        device_name = "Main Biometric Device"

    local_ip = input("Device Local IP (*************): ").strip()
    if not local_ip:
        local_ip = "*************"

    local_port = input("Device Local Port (32150): ").strip()
    if not local_port:
        local_port = "32150"

    try:
        local_port = int(local_port)
    except ValueError:
        local_port = 32150

    cloud_enabled = input("Enable cloud connectivity for this device? (y/n): ").strip().lower()
    cloud_enabled = cloud_enabled in ['y', 'yes']

    # Create device configuration
    device_config = DeviceConfig(
        device_id=device_id,
        device_name=device_name,
        local_ip=local_ip,
        local_port=local_port,
        cloud_enabled=cloud_enabled
    )

    # Add device to configuration
    if config_manager.add_device(device_config):
        print(f"✅ Device {device_id} configured successfully!")
        return True
    else:
        print(f"❌ Failed to configure device {device_id}")
        return False

def main():
    """Main function to configure network for ZK device"""
    print("ZK Biometric Device Network Configuration")
    print("=" * 50)

    # Ask user for configuration type
    print("Choose configuration type:")
    print("1. Ethernet (Direct IP connection)")
    print("2. Cloud (Internet-based connection)")
    print("3. Both (Hybrid setup)")

    choice = input("Enter choice (1/2/3): ").strip()

    if choice in ['1', '3']:
        # Ethernet configuration
        print("\n🔌 Ethernet Configuration")
        print("=" * 30)

        # Updated settings for your ZK device communication
        config = {
            'ip_address': '*************',
            'subnet_mask': '*************',
            'gateway': '************',
            'port': '32150'
        }

        # Get user input
        print(f"Current configuration:")
        print(f"IP Address: {config['ip_address']}")
        print(f"Subnet Mask: {config['subnet_mask']}")
        print(f"Gateway: {config['gateway']}")
        print()

        response = input("Do you want to use these settings? (y/n): ").lower()

        if response != 'y':
            config['ip_address'] = input("Enter IP address (*************): ") or config['ip_address']
            config['subnet_mask'] = input("Enter subnet mask (*************): ") or config['subnet_mask']
            config['gateway'] = input("Enter gateway (***********): ") or config['gateway']

        # Initialize network config
        net_config = NetworkConfig()

        # Show available interfaces
        interfaces = net_config.get_network_interfaces()
        if interfaces:
            print(f"\nAvailable network interfaces:")
            for i, interface in enumerate(interfaces):
                print(f"{i+1}. {interface}")

            try:
                choice = input(f"\nSelect interface (1-{len(interfaces)}) or press Enter for auto: ")
                if choice:
                    interface_name = interfaces[int(choice) - 1]
                else:
                    interface_name = None
            except (ValueError, IndexError):
                interface_name = None
        else:
            interface_name = None

        # Configure network
        print(f"\nConfiguring Ethernet network...")
        # Remove port from config as it's not needed for network configuration
        network_config = {k: v for k, v in config.items() if k != 'port'}
        success = net_config.configure_network(
            interface_name=interface_name,
            **network_config
        )

        if success:
            print("✅ Ethernet network configuration completed successfully!")

            # Test connectivity
            print(f"\nTesting connectivity to ZK device...")
            if net_config.ping_device(config['ip_address']):
                print("✅ ZK device is reachable via Ethernet!")
            else:
                print("❌ Cannot reach ZK device via Ethernet. Please check:")
                print("   - Device is powered on")
                print("   - Network cable is connected")
                print("   - Device IP matches configuration")
        else:
            print("❌ Ethernet network configuration failed!")
            print("Please run as administrator/sudo and try again.")

    if choice in ['2', '3']:
        # Cloud configuration
        if CLOUD_ENABLED:
            cloud_success = setup_cloud_config()
            if cloud_success:
                device_success = setup_device_config()
                if device_success:
                    print("\n✅ Cloud configuration completed successfully!")
                    print("You can now use cloud connectivity for your ZK biometric devices.")
                    print("\nNext steps:")
                    print("1. Start your Flask application")
                    print("2. The cloud connector will automatically start")
                    print("3. Check cloud status in the admin dashboard")
                else:
                    print("\n❌ Device configuration failed!")
            else:
                print("\n❌ Cloud configuration failed!")
        else:
            print("\n❌ Cloud functionality not available!")
            print("Please install cloud dependencies:")
            print("pip install requests websocket-client paho-mqtt cryptography python-dotenv")

    print("\n" + "=" * 50)
    print("Configuration completed!")

    if choice == '3':
        print("\n📋 Summary:")
        print("- Ethernet connectivity configured for direct device access")
        print("- Cloud connectivity configured for remote access")
        print("- Your system now supports both connection methods")
        print("- The application will automatically choose the best connection method")
    elif choice == '1':
        print("\n📋 Summary:")
        print("- Ethernet connectivity configured")
        print("- Direct device access available")
    elif choice == '2':
        print("\n📋 Summary:")
        print("- Cloud connectivity configured")
        print("- Remote device access available")
        print("- Requires internet connection")

if __name__ == '__main__':
    main()
