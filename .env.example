# ZK Biometric Cloud Configuration
# Copy this file to .env and update with your actual values

# Flask Application Settings
SECRET_KEY=your_secret_key_here_change_this_in_production
FLASK_ENV=development
FLASK_DEBUG=true

# Cloud Service Configuration
CLOUD_API_BASE_URL=http://*************:32150
CLOUD_WEBSOCKET_URL=ws://*************:32150
CLOUD_MQTT_BROKER=*************
CLOUD_MQTT_PORT=32150

# Authentication
CLOUD_API_KEY=your_api_key_here
CLOUD_SECRET_KEY=your_secret_key_here
CLOUD_ORG_ID=your_organization_id

# Connection Settings
CLOUD_USE_SSL=true
CLOUD_VERIFY_SSL=true
CLOUD_CONNECTION_TIMEOUT=30
CLOUD_RETRY_ATTEMPTS=3
CLOUD_HEARTBEAT_INTERVAL=60

# Sync Settings
CLOUD_AUTO_SYNC=true
CLOUD_SYNC_INTERVAL=30
CLOUD_BATCH_SIZE=100

# Security Settings
CLOUD_ENCRYPTION_ENABLED=true

# Backup Settings
CLOUD_LOCAL_BACKUP=true
CLOUD_BACKUP_RETENTION_DAYS=30

# Database Configuration (Optional - for MySQL backup)
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=yourpass
MYSQL_DATABASE=staff
MYSQL_PORT=3306

# Redis Configuration (Optional - for caching and queuing)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# Celery Configuration (Optional - for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=zk_biometric.log

# Device Configuration (can also be set in cloud_config.json)
DEFAULT_DEVICE_IP=*************
DEFAULT_DEVICE_PORT=32150
DEFAULT_DEVICE_TIMEOUT=5

# Network Configuration
NETWORK_INTERFACE=auto
NETWORK_SUBNET=*************
NETWORK_GATEWAY=***********

# Performance Settings
MAX_CONCURRENT_CONNECTIONS=50
REQUEST_TIMEOUT=30
MAX_RETRY_ATTEMPTS=3
QUEUE_MAX_SIZE=1000

# Feature Flags
ENABLE_CLOUD_FEATURES=true
ENABLE_WEBSOCKET=true
ENABLE_MQTT=false
ENABLE_API_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true

# Development Settings (only for development)
DEV_MOCK_DEVICES=false
DEV_SKIP_DEVICE_VALIDATION=false
DEV_ENABLE_DEBUG_ROUTES=false
