#!/usr/bin/env python3
"""
ZK Device 181 - Web Interface Connection
Try connecting via HTTP/HTTPS web interface
"""

import requests
import json
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Disable SSL warnings for testing
urllib3.disable_warnings(InsecureRequestWarning)

def test_web_interface():
    """Test web interface connection"""
    print("🌐 Testing ZK Device Web Interface")
    print("=" * 50)
    
    # Test different web interface URLs
    urls = [
        "http://*************",
        "http://*************:32150",
        "https://*************",
        "https://*************:32150",
        "http://*************:80",
        "https://*************:443"
    ]
    
    for url in urls:
        print(f"\nTesting: {url}")
        try:
            response = requests.get(url, timeout=10, verify=False)
            print(f"✅ SUCCESS: {url}")
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
            
            # Check if it's a ZK device interface
            content = response.text.lower()
            if any(keyword in content for keyword in ['zkteco', 'biometric', 'attendance', 'device']):
                print(f"   🎯 Likely ZK device interface!")
                return url, response
            else:
                print(f"   ℹ️ Web service found but may not be ZK device")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed: {e}")
    
    return None, None

def test_api_endpoints():
    """Test common ZK device API endpoints"""
    print("\n🔗 Testing ZK Device API Endpoints")
    print("=" * 50)
    
    base_urls = [
        "http://*************:32150",
        "http://*************",
        "https://*************:32150"
    ]
    
    # Common ZK API endpoints
    endpoints = [
        "/api/device/info",
        "/api/users",
        "/api/attendance",
        "/device/info",
        "/users",
        "/attendance",
        "/cgi-bin/AccessUser.cgi",
        "/cgi-bin/AccessRecord.cgi",
        "/zkapi/device",
        "/rest/device/info"
    ]
    
    for base_url in base_urls:
        print(f"\nTesting base URL: {base_url}")
        
        for endpoint in endpoints:
            url = base_url + endpoint
            try:
                response = requests.get(url, timeout=5, verify=False)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - Status: {response.status_code}")
                    try:
                        data = response.json()
                        print(f"   📄 JSON response: {str(data)[:100]}...")
                    except:
                        print(f"   📄 Response: {response.text[:100]}...")
                elif response.status_code == 401:
                    print(f"🔐 {endpoint} - Requires authentication")
                elif response.status_code == 404:
                    print(f"❌ {endpoint} - Not found")
                else:
                    print(f"⚠️ {endpoint} - Status: {response.status_code}")
                    
            except requests.exceptions.RequestException:
                pass  # Skip failed requests

def test_zk_cloud_api():
    """Test ZK cloud API with device credentials"""
    print("\n☁️ Testing ZK Cloud API")
    print("=" * 50)
    
    # Try with device credentials
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'ZK-Device-Client'
    }
    
    # Test authentication
    auth_data = {
        'device_id': '181',
        'common_key': '1302',
        'username': 'admin',
        'password': 'admin'
    }
    
    urls = [
        "http://*************:32150/api/auth",
        "http://*************:32150/api/login",
        "http://*************:32150/auth",
        "http://*************:32150/login"
    ]
    
    for url in urls:
        try:
            print(f"Testing auth: {url}")
            response = requests.post(url, json=auth_data, headers=headers, timeout=10, verify=False)
            print(f"   Status: {response.status_code}")
            if response.status_code in [200, 201]:
                print(f"   ✅ Authentication successful!")
                print(f"   Response: {response.text[:200]}...")
            elif response.status_code == 401:
                print(f"   🔐 Authentication required")
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Error: {e}")

def open_browser_interface():
    """Open browser to device interface"""
    print("\n🌐 Opening Browser Interface")
    print("=" * 50)
    
    urls_to_try = [
        "http://*************:32150",
        "http://*************",
        "https://*************:32150",
        "https://*************"
    ]
    
    print("You can manually open these URLs in your browser:")
    for url in urls_to_try:
        print(f"   🔗 {url}")
    
    print("\nCommon login credentials to try:")
    print("   Username: admin, Password: admin")
    print("   Username: admin, Password: 123456")
    print("   Username: admin, Password: (empty)")
    print("   Device ID: 181, Common Key: 1302")

def provide_next_steps():
    """Provide next steps for connection"""
    print("\n🎯 NEXT STEPS TO CONNECT")
    print("=" * 50)
    
    print("1. 🌐 Try Web Interface:")
    print("   - Open http://*************:32150 in browser")
    print("   - Login with admin credentials")
    print("   - Check device settings and communication mode")
    
    print("\n2. 🔧 Check Device Settings:")
    print("   - Verify device is in 'PC Connection' mode")
    print("   - Check if 'TCP/IP' communication is enabled")
    print("   - Verify device ID is set to 181")
    print("   - Confirm common key is 1302")
    
    print("\n3. 🔐 Authentication Issues:")
    print("   - Device may require specific authentication")
    print("   - Try different connection protocols")
    print("   - Check if device requires encryption")
    
    print("\n4. 🛠️ Alternative Connection Methods:")
    print("   - Use ZKTeco official software")
    print("   - Try different SDK/library")
    print("   - Connect via RS485/USB if available")
    
    print("\n5. 📞 Contact Support:")
    print("   - Check device manual for connection details")
    print("   - Contact device administrator")
    print("   - Verify device model and firmware version")

def main():
    """Main function"""
    print("🔧 ZK DEVICE 181 - WEB INTERFACE CONNECTION TEST")
    print("=" * 60)
    print("Device: *************:32150")
    print("Device ID: 181, Common Key: 1302")
    print("=" * 60)
    
    # Test web interface
    url, response = test_web_interface()
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test cloud API
    test_zk_cloud_api()
    
    # Provide browser links
    open_browser_interface()
    
    # Provide next steps
    provide_next_steps()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print("✅ Network: Working")
    print("✅ Port 32150: Accessible")
    print("❌ ZK Protocol: Failed (likely authentication/protocol issue)")
    print("✅ Web Services: Available on ports 80, 443, 32150")
    
    print("\n🎯 RECOMMENDATION:")
    print("Try connecting via web interface first to verify device settings")
    print("Then configure proper authentication for ZK protocol connection")

if __name__ == '__main__':
    main()
