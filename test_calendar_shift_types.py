#!/usr/bin/env python3
"""
Test calendar data to verify shift types are included
"""

import requests
import json
from datetime import datetime, timedelta

def test_calendar_data(staff_id='888', password='password123', school_id=4):
    """Test calendar data includes shift types"""
    
    print(f"=== Testing Calendar Data for Staff ID: {staff_id} ===")
    
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Create session and login
        session = requests.Session()
        
        # Login
        login_data = {
            'school_id': school_id,
            'username': staff_id,
            'password': password
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        login_result = login_response.json()
        if 'error' in login_result:
            print(f"❌ Login error: {login_result['error']}")
            return False
        
        print("✅ Login successful")
        
        # Test calendar data endpoints
        today = datetime.now()
        start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = (today + timedelta(days=30)).strftime('%Y-%m-%d')
        
        # Test /get_staff_attendance endpoint
        print("\n1. Testing /get_staff_attendance endpoint...")
        attendance_url = f"{base_url}/get_staff_attendance?start={start_date}&end={end_date}"
        attendance_response = session.get(attendance_url)
        
        if attendance_response.status_code == 200:
            attendance_data = attendance_response.json()
            if attendance_data.get('success'):
                print("✅ /get_staff_attendance endpoint working")
                
                # Check if attendance records include shift_type
                attendance_records = attendance_data.get('attendance', [])
                print(f"📊 Found {len(attendance_records)} attendance records")
                
                if attendance_records:
                    sample_record = attendance_records[0]
                    print(f"\n📝 Sample attendance record:")
                    for key, value in sample_record.items():
                        print(f"   {key}: {value}")
                    
                    if 'shift_type' in sample_record:
                        print("✅ shift_type field is present in attendance data")
                    else:
                        print("❌ shift_type field is missing from attendance data")
                else:
                    print("⚠️ No attendance records found")
            else:
                print(f"❌ /get_staff_attendance failed: {attendance_data.get('error')}")
        else:
            print(f"❌ /get_staff_attendance request failed: {attendance_response.status_code}")
        
        # Test /staff/attendance_calendar endpoint
        print("\n2. Testing /staff/attendance_calendar endpoint...")
        calendar_url = f"{base_url}/staff/attendance_calendar?start={start_date}&end={end_date}"
        calendar_response = session.get(calendar_url)
        
        if calendar_response.status_code == 200:
            calendar_data = calendar_response.json()
            if calendar_data.get('success'):
                print("✅ /staff/attendance_calendar endpoint working")
                
                # Check if calendar records include shift_type
                calendar_records = calendar_data.get('attendance', [])
                print(f"📊 Found {len(calendar_records)} calendar records")
                
                if calendar_records:
                    sample_record = calendar_records[0]
                    print(f"\n📝 Sample calendar record:")
                    for key, value in sample_record.items():
                        print(f"   {key}: {value}")
                    
                    if 'shift_type' in sample_record:
                        print("✅ shift_type field is present in calendar data")
                    else:
                        print("❌ shift_type field is missing from calendar data")
                else:
                    print("⚠️ No calendar records found")
            else:
                print(f"❌ /staff/attendance_calendar failed: {calendar_data.get('error')}")
        else:
            print(f"❌ /staff/attendance_calendar request failed: {calendar_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def check_staff_shift_types():
    """Check what shift types are set for staff"""
    
    print("\n=== Checking Staff Shift Types in Database ===")
    
    import sqlite3
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT id, staff_id, full_name, shift_type FROM staff')
        staff_records = cursor.fetchall()
        
        print(f"Found {len(staff_records)} staff members:")
        for staff in staff_records:
            shift_type = staff['shift_type'] or 'Not Set'
            print(f"  - {staff['full_name']} (ID: {staff['staff_id']}) - Shift: {shift_type}")
        
        return staff_records
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return []
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== Calendar Shift Type Test ===\n")
    
    # Check database first
    staff_records = check_staff_shift_types()
    
    # Test calendar data
    if staff_records:
        for staff in staff_records:
            print(f"\n{'='*60}")
            test_calendar_data(staff['staff_id'], 'password123')
    
    print(f"\n{'='*60}")
    print("SUMMARY:")
    print("1. Calendar should now show shift types in event titles")
    print("2. Event details should include shift type information")
    print("3. Both /get_staff_attendance and /staff/attendance_calendar should include shift_type")
    print("4. JavaScript will format shift types as 'General', 'Over', etc.")
    print("\nTo see the changes:")
    print("1. Login to staff dashboard")
    print("2. Look at the calendar view")
    print("3. Click on any attendance event to see details")
    print("4. Shift type should be displayed in the event title and details")
