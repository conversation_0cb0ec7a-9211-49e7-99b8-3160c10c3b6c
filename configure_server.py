#!/usr/bin/env python3
"""
ZK Biometric Server Configuration Script
Configure the system to use your specific server: *************:32150
"""

import os
import json
import sys

def print_header():
    """Print configuration header"""
    print("=" * 70)
    print("🔧 ZK BIOMETRIC SERVER CONFIGURATION")
    print("=" * 70)
    print("Configuring system for your server:")
    print("📡 Server Address: *************")
    print("🔌 Server Port: 32150")
    print()

def configure_cloud_settings():
    """Configure cloud settings for your server"""
    print("📋 Step 1: Configuring Cloud Settings")
    print("-" * 40)
    
    try:
        from cloud_config import CloudConfigManager
        
        # Create configuration manager
        config_manager = CloudConfigManager()
        
        # Update configuration for your server
        config = config_manager.config
        config.api_base_url = "http://*************:32150"
        config.websocket_url = "ws://*************:32150"
        config.mqtt_broker = "*************"
        config.mqtt_port = 32150
        config.use_ssl = False  # HTTP instead of HTTPS for your server
        config.verify_ssl = False
        
        # Save configuration
        config_manager.save_config()
        
        print("✅ Cloud configuration updated successfully")
        print(f"   - API URL: {config.api_base_url}")
        print(f"   - WebSocket URL: {config.websocket_url}")
        print(f"   - MQTT Broker: {config.mqtt_broker}:{config.mqtt_port}")
        print(f"   - SSL: {config.use_ssl}")
        
        return True
        
    except ImportError:
        print("⚠️  Cloud modules not available")
        return False
    except Exception as e:
        print(f"❌ Error configuring cloud settings: {e}")
        return False

def configure_device_settings():
    """Configure device settings for your server"""
    print("\n📱 Step 2: Configuring Device Settings")
    print("-" * 40)
    
    try:
        from cloud_config import CloudConfigManager, DeviceConfig
        
        config_manager = CloudConfigManager()
        
        # Remove existing devices
        config_manager.devices = []
        
        # Add device configuration for your server
        device = DeviceConfig(
            device_id="ZK_SERVER_001",
            device_name="ZK Biometric Server Device",
            local_ip="*************",
            local_port=32150,
            cloud_enabled=True,
            sync_interval=30
        )
        
        # Add device
        success = config_manager.add_device(device)
        
        if success:
            print("✅ Device configuration updated successfully")
            print(f"   - Device ID: {device.device_id}")
            print(f"   - Device Name: {device.device_name}")
            print(f"   - Server IP: {device.local_ip}")
            print(f"   - Server Port: {device.local_port}")
            print(f"   - Cloud Enabled: {device.cloud_enabled}")
            return True
        else:
            print("❌ Failed to add device configuration")
            return False
            
    except ImportError:
        print("⚠️  Cloud modules not available")
        return False
    except Exception as e:
        print(f"❌ Error configuring device settings: {e}")
        return False

def test_server_connection():
    """Test connection to your server"""
    print("\n🔍 Step 3: Testing Server Connection")
    print("-" * 40)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Test Ethernet connection to your server
        print("Testing Ethernet connection...")
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=10,
            use_cloud=False
        )
        
        print(f"Attempting to connect to *************:32150...")
        
        if device.connect():
            print("✅ Successfully connected to your server!")
            
            # Try to get basic info
            try:
                users = device.get_users()
                print(f"✅ Found {len(users)} users on the device")
            except Exception as e:
                print(f"⚠️  Connected but couldn't get users: {e}")
            
            device.disconnect()
            return True
        else:
            print("❌ Failed to connect to your server")
            print("💡 Please check:")
            print("   - Server is running and accessible")
            print("   - Port 32150 is open")
            print("   - Network connectivity")
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def create_server_startup_script():
    """Create startup script for your server configuration"""
    print("\n📝 Step 4: Creating Startup Script")
    print("-" * 40)
    
    script_content = '''@echo off
echo Starting ZK Biometric System with Server Configuration
echo Server: *************:32150
echo.

echo Checking configuration...
python configure_server.py

echo.
echo Starting Flask application...
python app.py

pause
'''
    
    try:
        with open('start_server_mode.bat', 'w') as f:
            f.write(script_content)
        
        print("✅ Created startup script: start_server_mode.bat")
        print("   You can double-click this file to start the system")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create startup script: {e}")
        return False

def update_documentation():
    """Update documentation with server details"""
    print("\n📚 Step 5: Updating Documentation")
    print("-" * 40)
    
    server_info = """
# Server Configuration

Your ZK Biometric system is now configured for:

**Server Details:**
- IP Address: *************
- Port: 32150
- Protocol: HTTP (not HTTPS)
- Connection Type: TCP/IP

**Usage:**
1. Start the system: `python app.py`
2. Access web interface: http://localhost:5000
3. Cloud dashboard: http://localhost:5000/cloud_dashboard

**Testing:**
- Test connection: `python configure_server.py`
- Run examples: `python cloud_example.py`
- Run tests: `python test_cloud_system.py`

**Configuration Files Updated:**
- .env (environment variables)
- cloud_config.json (device settings)
- All example scripts updated with your server details
"""
    
    try:
        with open('SERVER_CONFIG.md', 'w') as f:
            f.write(server_info)
        
        print("✅ Created SERVER_CONFIG.md with your server details")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create documentation: {e}")
        return False

def main():
    """Main configuration function"""
    print_header()
    
    print("🚀 Starting server configuration...")
    print()
    
    # Run configuration steps
    steps = [
        ("Configure Cloud Settings", configure_cloud_settings),
        ("Configure Device Settings", configure_device_settings),
        ("Test Server Connection", test_server_connection),
        ("Create Startup Script", create_server_startup_script),
        ("Update Documentation", update_documentation)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name} completed with warnings")
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 CONFIGURATION SUMMARY")
    print("=" * 70)
    
    if success_count >= 4:
        print("🎉 Server configuration completed successfully!")
        print()
        print("✅ Your system is now configured for:")
        print("   📡 Server: *************:32150")
        print("   🌐 Cloud connectivity enabled")
        print("   🔧 Device settings updated")
        print()
        print("🚀 Next Steps:")
        print("1. Start the application: python app.py")
        print("2. Access web interface: http://localhost:5000")
        print("3. Test device connection in admin dashboard")
        print()
        print("📝 Quick Start:")
        print("   Double-click: start_server_mode.bat")
        
    else:
        print("⚠️  Configuration completed with some issues")
        print(f"   {success_count}/{len(steps)} steps successful")
        print()
        print("💡 You can still use the system, but please review any errors above")
        print("   Manual configuration may be needed for some features")
    
    print("\n📚 Documentation:")
    print("   - SERVER_CONFIG.md (your server details)")
    print("   - CLOUD_MIGRATION_GUIDE.md (complete guide)")
    print("   - README.md (general documentation)")

if __name__ == '__main__':
    main()
