#!/usr/bin/env python3
"""
Create test biometric verification data for existing staff members
"""

import sqlite3
import datetime
import random

def create_test_verifications():
    """Create test verification data for staff members who don't have any"""
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get all staff members
        cursor.execute('SELECT id, staff_id, full_name, school_id FROM staff')
        all_staff = cursor.fetchall()
        
        # Get staff members who already have verification data
        cursor.execute('SELECT DISTINCT staff_id FROM biometric_verifications')
        staff_with_verifications = [row[0] for row in cursor.fetchall()]
        
        print(f"Found {len(all_staff)} total staff members")
        print(f"Staff with existing verifications: {staff_with_verifications}")
        
        # Find staff without verification data
        staff_without_verifications = [
            staff for staff in all_staff 
            if staff[0] not in staff_with_verifications
        ]
        
        print(f"Staff without verifications: {len(staff_without_verifications)}")
        for staff in staff_without_verifications:
            print(f"  - ID: {staff[0]}, Staff Number: {staff[1]}, Name: {staff[2]}")
        
        if not staff_without_verifications:
            print("All staff members already have verification data!")
            return
        
        # Create test verification data for today and recent days
        today = datetime.date.today()
        
        for staff in staff_without_verifications:
            staff_id, staff_number, full_name, school_id = staff
            
            print(f"\nCreating test verifications for {full_name} (ID: {staff_id})...")
            
            # Create verifications for the last 7 days
            for days_ago in range(7):
                test_date = today - datetime.timedelta(days=days_ago)
                
                # Create morning check-in
                checkin_time = test_date.strftime('%Y-%m-%d') + ' ' + f"{random.randint(9, 10):02d}:{random.randint(15, 30):02d}:{random.randint(0, 59):02d}"
                
                cursor.execute('''
                    INSERT INTO biometric_verifications 
                    (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    staff_id, school_id, 'check-in', checkin_time,
                    '*************', 'fingerprint', 'success',
                    f'Test verification for {full_name}'
                ))
                
                # Create afternoon check-out (70% chance)
                if random.random() < 0.7:
                    checkout_time = test_date.strftime('%Y-%m-%d') + ' ' + f"{random.randint(16, 17):02d}:{random.randint(0, 59):02d}:{random.randint(0, 59):02d}"
                    
                    cursor.execute('''
                        INSERT INTO biometric_verifications 
                        (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        staff_id, school_id, 'check-out', checkout_time,
                        '*************', 'fingerprint', 'success',
                        f'Test verification for {full_name}'
                    ))
                
                # Create overtime-in (30% chance)
                if random.random() < 0.3:
                    overtime_in_time = test_date.strftime('%Y-%m-%d') + ' ' + f"{random.randint(17, 18):02d}:{random.randint(0, 30):02d}:{random.randint(0, 59):02d}"
                    
                    cursor.execute('''
                        INSERT INTO biometric_verifications 
                        (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        staff_id, school_id, 'overtime-in', overtime_in_time,
                        '*************', 'fingerprint', 'success',
                        f'Test verification for {full_name}'
                    ))
                    
                    # Create overtime-out if overtime-in exists
                    overtime_out_time = test_date.strftime('%Y-%m-%d') + ' ' + f"{random.randint(18, 19):02d}:{random.randint(0, 59):02d}:{random.randint(0, 59):02d}"
                    
                    cursor.execute('''
                        INSERT INTO biometric_verifications 
                        (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status, notes)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        staff_id, school_id, 'overtime-out', overtime_out_time,
                        '*************', 'fingerprint', 'success',
                        f'Test verification for {full_name}'
                    ))
            
            print(f"  ✅ Created test verifications for {full_name}")
        
        # Commit all changes
        conn.commit()
        
        # Verify the data was created
        cursor.execute('SELECT COUNT(*) FROM biometric_verifications')
        total_verifications = cursor.fetchone()[0]
        print(f"\n✅ Total biometric verifications in database: {total_verifications}")
        
        # Show verification count by staff
        cursor.execute('''
            SELECT s.staff_id, s.full_name, COUNT(bv.id) as verification_count
            FROM staff s
            LEFT JOIN biometric_verifications bv ON s.id = bv.staff_id
            GROUP BY s.id, s.staff_id, s.full_name
            ORDER BY verification_count DESC
        ''')
        
        staff_verification_counts = cursor.fetchall()
        print("\nVerification counts by staff:")
        for staff_number, name, count in staff_verification_counts:
            print(f"  - {staff_number} ({name}): {count} verifications")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 Creating Test Biometric Verification Data")
    print("=" * 50)
    
    success = create_test_verifications()
    
    if success:
        print("\n✅ Test verification data created successfully!")
        print("Now the staff dashboard and admin profile views should show verification history.")
    else:
        print("\n❌ Failed to create test verification data")
