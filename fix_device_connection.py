#!/usr/bin/env python3
"""
Fix Device Connection Issues
Diagnose and fix the "Failed to connect to device via Ethernet" error
"""

import os
import socket
import time

# Disable cloud to avoid conflicts
os.environ['DISABLE_CLOUD'] = '1'

def test_network_connectivity():
    """Test basic network connectivity to devices"""
    print("🌐 Testing Network Connectivity")
    print("-" * 40)
    
    devices = [
        {"ip": "*************", "port": 32150, "name": "Primary Device (32150)"},
        {"ip": "*************", "port": 4370, "name": "Primary Device (4370)"},
        {"ip": "*************", "port": 80, "name": "Web Interface"},
        {"ip": "*************", "port": 4370, "name": "Factory Default"},
    ]
    
    accessible_devices = []
    
    for device in devices:
        try:
            print(f"Testing {device['name']} ({device['ip']}:{device['port']})...", end=" ")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((device['ip'], device['port']))
            sock.close()
            
            if result == 0:
                print("✅ ACCESSIBLE")
                accessible_devices.append(device)
            else:
                print("❌ Not accessible")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return accessible_devices

def test_zk_device_connection(device_ip, port=4370, device_id="1"):
    """Test ZK device connection with specific parameters"""
    print(f"\n🔧 Testing ZK Device Connection")
    print(f"IP: {device_ip}, Port: {port}, Device ID: {device_id}")
    print("-" * 40)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Create device with specific parameters
        device = ZKBiometricDevice(
            device_ip=device_ip,
            port=port,
            timeout=15,
            device_id=device_id,
            use_cloud=False
        )
        
        print("Device object created successfully")
        print(f"  Device IP: {device.device_ip}")
        print(f"  Port: {device.port}")
        print(f"  Timeout: {device.timeout}")
        print(f"  Device ID: {device.device_id}")
        print(f"  Use Cloud: {device.use_cloud}")
        
        print("\nAttempting connection...")
        if device.connect():
            print("✅ SUCCESS! Device connected")
            
            try:
                users = device.get_users()
                print(f"✅ Found {len(users)} users on device")
                
                # Try to get attendance records
                records = device.get_attendance_records()
                print(f"✅ Found {len(records)} attendance records")
                
                device.disconnect()
                return True
                
            except Exception as e:
                print(f"⚠️ Connected but limited access: {e}")
                device.disconnect()
                return True
                
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error creating/testing device: {e}")
        return False

def test_all_possible_configurations():
    """Test all possible device configurations"""
    print("\n🔍 Testing All Possible Configurations")
    print("=" * 50)
    
    configurations = [
        # Primary device configurations
        {"ip": "*************", "port": 32150, "device_id": "181", "name": "Primary (32150, ID 181)"},
        {"ip": "*************", "port": 32150, "device_id": "1", "name": "Primary (32150, ID 1)"},
        {"ip": "*************", "port": 4370, "device_id": "181", "name": "Primary (4370, ID 181)"},
        {"ip": "*************", "port": 4370, "device_id": "1", "name": "Primary (4370, ID 1)"},
        
        # Factory default configurations
        {"ip": "*************", "port": 4370, "device_id": "1", "name": "Factory Default (4370, ID 1)"},
        {"ip": "*************", "port": 32150, "device_id": "181", "name": "Factory Default (32150, ID 181)"},
    ]
    
    working_configs = []
    
    for config in configurations:
        print(f"\n🧪 Testing: {config['name']}")
        print(f"   IP: {config['ip']}, Port: {config['port']}, Device ID: {config['device_id']}")
        
        if test_zk_device_connection(config['ip'], config['port'], config['device_id']):
            working_configs.append(config)
            print(f"   ✅ WORKING CONFIGURATION FOUND!")
        else:
            print(f"   ❌ Configuration failed")
    
    return working_configs

def update_app_configuration(working_config):
    """Update app.py with working configuration"""
    print(f"\n📝 Updating App Configuration")
    print("-" * 40)
    
    if not working_config:
        print("❌ No working configuration to update")
        return False
    
    config = working_config[0]  # Use first working config
    
    print(f"Using configuration: {config['name']}")
    print(f"  IP: {config['ip']}")
    print(f"  Port: {config['port']}")
    print(f"  Device ID: {config['device_id']}")
    
    # Create a configuration update script
    update_script = f'''#!/usr/bin/env python3
"""
Update Device Configuration in App
Apply working device configuration to app.py
"""

import re

def update_app_config():
    """Update app.py with working device configuration"""
    
    # Read app.py
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update default device IP addresses
    # Replace various default IPs with working IP
    replacements = [
        (r"device_ip = request\\.form\\.get\\('device_ip', '[^']*'\\)", 
         f"device_ip = request.form.get('device_ip', '{config['ip']}')"),
        (r"device_ip = request\\.args\\.get\\('device_ip', '[^']*'\\)",
         f"device_ip = request.args.get('device_ip', '{config['ip']}')"),
        (r"device_ip = '[^']*'  # Default device IP",
         f"device_ip = '{config['ip']}'  # Default device IP"),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Save updated app.py
    with open('app_updated.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Updated configuration saved to app_updated.py")
    print("To apply: copy app_updated.py to app.py")

if __name__ == '__main__':
    update_app_config()
'''
    
    try:
        with open('update_app_config.py', 'w') as f:
            f.write(update_script)
        
        print("✅ Created update_app_config.py")
        print("Run this script to update app.py with working configuration")
        return True
        
    except Exception as e:
        print(f"❌ Error creating update script: {e}")
        return False

def create_test_connection_script(working_config):
    """Create a test script for the working configuration"""
    if not working_config:
        return
    
    config = working_config[0]
    
    test_script = f'''#!/usr/bin/env python3
"""
Test Working Device Configuration
Test the device with the working configuration found
"""

import os
os.environ['DISABLE_CLOUD'] = '1'

from zk_biometric import ZKBiometricDevice

def test_working_config():
    print("🧪 Testing Working Device Configuration")
    print("=" * 50)
    print(f"IP: {config['ip']}")
    print(f"Port: {config['port']}")
    print(f"Device ID: {config['device_id']}")
    print("=" * 50)
    
    try:
        device = ZKBiometricDevice(
            device_ip='{config['ip']}',
            port={config['port']},
            timeout=15,
            device_id='{config['device_id']}',
            use_cloud=False
        )
        
        if device.connect():
            print("✅ SUCCESS! Device connected")
            
            users = device.get_users()
            print(f"📱 Users: {{len(users)}}")
            
            records = device.get_attendance_records()
            print(f"📊 Records: {{len(records)}}")
            
            device.disconnect()
            print("🎉 Device is working perfectly!")
            return True
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {{e}}")
        return False

if __name__ == '__main__':
    test_working_config()
'''
    
    try:
        with open('test_working_config.py', 'w') as f:
            f.write(test_script)
        print("✅ Created test_working_config.py")
    except Exception as e:
        print(f"❌ Error creating test script: {e}")

def main():
    """Main fix function"""
    print("🔧 DEVICE CONNECTION FIX TOOL")
    print("=" * 60)
    print("Diagnosing and fixing 'Failed to connect to device via Ethernet' error")
    print("=" * 60)
    
    # Step 1: Test network connectivity
    accessible_devices = test_network_connectivity()
    
    if not accessible_devices:
        print("\n❌ No devices are accessible via network")
        print("Check device power, network connection, and IP addresses")
        return
    
    print(f"\n✅ Found {len(accessible_devices)} accessible devices")
    
    # Step 2: Test ZK device connections
    working_configs = test_all_possible_configurations()
    
    if working_configs:
        print(f"\n🎉 SUCCESS! Found {len(working_configs)} working configurations:")
        for config in working_configs:
            print(f"   ✅ {config['name']}")
        
        # Step 3: Update app configuration
        update_app_configuration(working_configs)
        
        # Step 4: Create test script
        create_test_connection_script(working_configs)
        
        print(f"\n📋 NEXT STEPS:")
        print("1. Run: python update_app_config.py")
        print("2. Copy app_updated.py to app.py")
        print("3. Restart your Flask application")
        print("4. Test with: python test_working_config.py")
        
    else:
        print(f"\n❌ No working ZK device configurations found")
        print("Device may need configuration via web interface:")
        print("1. Go to: http://*************")
        print("2. Login: admin/123456")
        print("3. Configure TCP/IP communication")
        print("4. Set Device ID: 181, Common Key: 1302, Port: 32150")

if __name__ == '__main__':
    main()
