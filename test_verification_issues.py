#!/usr/bin/env python3
"""
Test script to diagnose verification history issues
"""

import sqlite3
import datetime
import json
from flask import Flask, session
import sys
import os

# Add current directory to path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_queries():
    """Test the database queries used in the routes"""
    print("=== Testing Database Queries ===")

    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Test 1: Check if we have staff data
        cursor.execute('SELECT id, staff_id, full_name FROM staff LIMIT 5')
        staff_records = cursor.fetchall()
        print(f"✅ Found {len(staff_records)} staff records")
        for staff in staff_records:
            print(f"  - Staff ID: {staff['id']}, Staff Number: {staff['staff_id']}, Name: {staff['full_name']}")

        if not staff_records:
            print("❌ No staff records found!")
            return False

        # Check which staff have verification data
        cursor.execute('SELECT DISTINCT staff_id FROM biometric_verifications ORDER BY staff_id')
        verification_staff_ids = [row[0] for row in cursor.fetchall()]
        print(f"Staff IDs with verification data: {verification_staff_ids}")

        # Find a staff member with verification data for testing
        test_staff_id = None
        if verification_staff_ids:
            test_staff_id = verification_staff_ids[0]
            print(f"Using staff ID {test_staff_id} for testing")
        else:
            print("❌ No staff members have verification data!")
            return False
            
        # Test 2: Check today's verifications for a specific staff member
        today = datetime.date.today()
        
        print(f"\n=== Testing Today's Verifications for Staff ID {test_staff_id} ===")
        cursor.execute('''
            SELECT verification_type, verification_time, biometric_method, verification_status
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) = ?
            ORDER BY verification_time DESC
        ''', (test_staff_id, today))
        
        today_verifications = cursor.fetchall()
        print(f"Today's verifications for staff {test_staff_id}: {len(today_verifications)}")
        for v in today_verifications:
            print(f"  - {v['verification_time']}: {v['verification_type']} ({v['biometric_method']}) - {v['verification_status']}")
        
        # Test 3: Check recent verifications (last 7 days)
        week_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).date()
        cursor.execute('''
            SELECT verification_type, verification_time, biometric_method, verification_status
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) >= ?
            ORDER BY verification_time DESC
            LIMIT 20
        ''', (test_staff_id, week_ago))
        
        recent_verifications = cursor.fetchall()
        print(f"\nRecent verifications (last 7 days) for staff {test_staff_id}: {len(recent_verifications)}")
        for v in recent_verifications[:5]:  # Show first 5
            print(f"  - {v['verification_time']}: {v['verification_type']} ({v['biometric_method']}) - {v['verification_status']}")
        
        # Test 4: Check get_today_attendance_status query
        print(f"\n=== Testing get_today_attendance_status Query ===")
        cursor.execute('''
            SELECT time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (test_staff_id, today))
        
        attendance = cursor.fetchone()
        if attendance:
            print(f"Today's attendance for staff {test_staff_id}:")
            print(f"  - Time in: {attendance['time_in']}")
            print(f"  - Time out: {attendance['time_out']}")
            print(f"  - Overtime in: {attendance['overtime_in']}")
            print(f"  - Overtime out: {attendance['overtime_out']}")
            print(f"  - Status: {attendance['status']}")
        else:
            print(f"No attendance record for staff {test_staff_id} today")
        
        # Test 5: Check comprehensive staff profile query
        print(f"\n=== Testing Comprehensive Staff Profile Query ===")
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).date()
        cursor.execute('''
            SELECT verification_type, verification_time, verification_status, device_ip
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) >= ?
            ORDER BY verification_time DESC
            LIMIT 50
        ''', (test_staff_id, thirty_days_ago))
        
        profile_verifications = cursor.fetchall()
        print(f"Profile verifications (last 30 days) for staff {test_staff_id}: {len(profile_verifications)}")
        for v in profile_verifications[:5]:  # Show first 5
            print(f"  - {v['verification_time']}: {v['verification_type']} - {v['verification_status']} ({v['device_ip']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Database query error: {e}")
        return False
    finally:
        conn.close()

def test_route_simulation():
    """Simulate the route responses"""
    print("\n=== Simulating Route Responses ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a test staff member
        cursor.execute('SELECT id FROM staff LIMIT 1')
        staff_record = cursor.fetchone()
        if not staff_record:
            print("❌ No staff records found for testing")
            return False
            
        staff_id = staff_record['id']
        today = datetime.date.today()
        
        # Simulate get_today_attendance_status
        print(f"Simulating get_today_attendance_status for staff {staff_id}...")
        
        # Get today's attendance
        attendance = cursor.execute('''
            SELECT time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        # Get today's verifications
        verifications = cursor.execute('''
            SELECT verification_type, verification_time, biometric_method, verification_status
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) = ?
            ORDER BY verification_time DESC
        ''', (staff_id, today)).fetchall()
        
        response_data = {
            'success': True,
            'attendance': dict(attendance) if attendance else None,
            'verifications': [dict(v) for v in verifications],
            'available_actions': []
        }
        
        print(f"Response data:")
        print(f"  - Attendance: {response_data['attendance']}")
        print(f"  - Verifications count: {len(response_data['verifications'])}")
        print(f"  - Verifications: {response_data['verifications']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Route simulation error: {e}")
        return False
    finally:
        conn.close()

def check_javascript_issues():
    """Check for potential JavaScript issues"""
    print("\n=== Checking JavaScript Issues ===")
    
    # Check if the JavaScript files exist
    js_files = [
        'static/js/staff_dashboard.js',
        'static/js/admin_dashboard.js'
    ]
    
    for js_file in js_files:
        if os.path.exists(js_file):
            print(f"✅ {js_file} exists")
            
            # Check for key functions
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'updateVerificationHistory' in content:
                print(f"  ✅ updateVerificationHistory function found")
            else:
                print(f"  ❌ updateVerificationHistory function NOT found")
                
            if 'loadTodayAttendanceStatus' in content:
                print(f"  ✅ loadTodayAttendanceStatus function found")
            else:
                print(f"  ❌ loadTodayAttendanceStatus function NOT found")
        else:
            print(f"❌ {js_file} does not exist")

if __name__ == "__main__":
    print("🔍 Diagnosing Verification History Issues")
    print("=" * 50)
    
    success = True
    success &= test_database_queries()
    success &= test_route_simulation()
    check_javascript_issues()
    
    if success:
        print("\n✅ Diagnosis completed - Check output above for issues")
    else:
        print("\n❌ Issues found during diagnosis")
