#!/usr/bin/env python3
"""
Test Factory Default Device Connection
Test connection to device at factory default IP *************:4370
"""

from zk_biometric import ZKBiometricDevice
import time

def test_factory_default():
    print("🧪 TESTING FACTORY DEFAULT DEVICE CONNECTION")
    print("=" * 60)
    print("Device IP: *************")
    print("Port: 4370")
    print("Device ID: 1 (factory default)")
    print("=" * 60)
    
    print("\n📡 Step 1: Creating device connection...")
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=4370,
            timeout=15,
            device_id='1',
            use_cloud=False
        )
        print("✅ Device object created successfully")
    except Exception as e:
        print(f"❌ Error creating device: {e}")
        return False
    
    print("\n🔗 Step 2: Attempting connection...")
    try:
        if device.connect():
            print("🎉 SUCCESS! Connected to Factory Default Device!")
            
            print("\n📊 Step 3: Getting device information...")
            try:
                users = device.get_users()
                print(f"✅ Users found: {len(users)}")
                
                records = device.get_attendance_records()
                print(f"✅ Attendance records: {len(records)}")
                
                # Try to get device info
                try:
                    info = device.get_device_info()
                    if info:
                        print(f"✅ Device info: {info}")
                except:
                    print("⚠️ Device info not available")
                
                print("\n🎯 Factory Default Device is working!")
                print("✅ You can now configure it to your target settings")
                device.disconnect()
                return True
                
            except Exception as e:
                print(f"⚠️ Connected but limited access: {e}")
                print("This is normal for factory defaults")
                device.disconnect()
                return True
                
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def provide_configuration_steps():
    print("\n📋 CONFIGURATION STEPS FOR FACTORY DEFAULT DEVICE")
    print("=" * 60)
    
    print("Since the factory default device is accessible, you can configure it:")
    print()
    
    print("🌐 METHOD 1: Web Interface (if available)")
    print("1. Try accessing: http://*************")
    print("2. Login with admin/123456")
    print("3. Configure network settings:")
    print("   - IP: *************")
    print("   - Device ID: 181")
    print("   - Common Key: 1302")
    print("   - Port: 32150")
    print()
    
    print("🔧 METHOD 2: ZK Software Configuration")
    print("1. Use manufacturer's ZK software")
    print("2. Connect to *************:4370")
    print("3. Configure network and communication settings")
    print()
    
    print("📱 METHOD 3: Device Menu (if device has display)")
    print("1. Access device menu via keypad")
    print("2. Navigate to Network/Communication settings")
    print("3. Configure IP, Device ID, and Common Key")

def main():
    """Main test function"""
    print("🔍 FACTORY DEFAULT DEVICE TEST")
    print("=" * 60)
    
    success = test_factory_default()
    
    if success:
        print("\n🎉 SUCCESS! Factory default device is accessible")
        provide_configuration_steps()
        
        print("\n📋 NEXT STEPS:")
        print("1. Configure the device using one of the methods above")
        print("2. Set target IP: *************")
        print("3. Set Device ID: 181")
        print("4. Set Common Key: 1302")
        print("5. Set Port: 32150")
        print("6. Test with: python simple_test_181.py")
    else:
        print("\n❌ Factory default device not accessible")
        print("Try manual hardware reset or check network connection")

if __name__ == '__main__':
    main()
