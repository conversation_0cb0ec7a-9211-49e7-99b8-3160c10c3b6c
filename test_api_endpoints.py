#!/usr/bin/env python3
"""
API Endpoints Test Suite for Enhanced Attendance Management System

This script tests all the new API endpoints:
- /get_regularization_requests
- /process_regularization_request
- /get_staff_regularization_history
- /get_staff_notifications
- /mark_notification_read
- Enhanced /staff/attendance_calendar
- Enhanced /get_staff_attendance
"""

import requests
import json
import sys
from datetime import datetime, date, timedelta


class APITestSuite:
    """Test suite for API endpoints"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def login_as_admin(self):
        """Login as admin for testing"""
        try:
            # Get login page first to get CSRF token
            response = self.session.get(f"{self.base_url}/")
            if response.status_code != 200:
                return False
            
            # Try to login as admin
            login_data = {
                'school_id': '1',
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            return response.status_code == 200 and 'admin_dashboard' in response.text
            
        except Exception as e:
            print(f"Admin login failed: {e}")
            return False
    
    def login_as_staff(self):
        """Login as staff for testing"""
        try:
            # Logout first
            self.session.get(f"{self.base_url}/logout")
            
            # Try to login as staff
            login_data = {
                'school_id': '1',
                'username': 'STAFF001',
                'password': 'staff123'
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            return response.status_code == 200 and 'staff_dashboard' in response.text
            
        except Exception as e:
            print(f"Staff login failed: {e}")
            return False
    
    def test_regularization_endpoints(self):
        """Test regularization-related endpoints"""
        print("\n=== Testing Regularization Endpoints ===")
        
        if not self.login_as_admin():
            self.log_test("Admin login for regularization tests", False, "Could not login as admin")
            return
        
        # Test get regularization requests
        try:
            response = self.session.get(f"{self.base_url}/get_regularization_requests")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Get regularization requests endpoint", True)
                else:
                    self.log_test("Get regularization requests endpoint", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Get regularization requests endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Get regularization requests endpoint", False, str(e))
        
        # Test process regularization request (mock data)
        try:
            process_data = {
                'request_id': '999',  # Non-existent ID for testing
                'decision': 'approved',
                'admin_reason': 'Test approval'
            }
            
            response = self.session.post(f"{self.base_url}/process_regularization_request", data=process_data)
            if response.status_code == 200:
                data = response.json()
                # Should fail because request doesn't exist, but endpoint should work
                self.log_test("Process regularization request endpoint", True, "Endpoint accessible")
            else:
                self.log_test("Process regularization request endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Process regularization request endpoint", False, str(e))
    
    def test_staff_endpoints(self):
        """Test staff-related endpoints"""
        print("\n=== Testing Staff Endpoints ===")
        
        if not self.login_as_staff():
            self.log_test("Staff login for staff tests", False, "Could not login as staff")
            return
        
        # Test get staff regularization history
        try:
            response = self.session.get(f"{self.base_url}/get_staff_regularization_history")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Get staff regularization history endpoint", True)
                else:
                    self.log_test("Get staff regularization history endpoint", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Get staff regularization history endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Get staff regularization history endpoint", False, str(e))
        
        # Test get staff notifications
        try:
            response = self.session.get(f"{self.base_url}/get_staff_notifications")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Get staff notifications endpoint", True)
                else:
                    self.log_test("Get staff notifications endpoint", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Get staff notifications endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Get staff notifications endpoint", False, str(e))
        
        # Test mark notification read (mock data)
        try:
            mark_data = {
                'notification_id': '999'  # Non-existent ID for testing
            }
            
            response = self.session.post(f"{self.base_url}/mark_notification_read", data=mark_data)
            if response.status_code == 200:
                data = response.json()
                # Should work even if notification doesn't exist
                self.log_test("Mark notification read endpoint", True, "Endpoint accessible")
            else:
                self.log_test("Mark notification read endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Mark notification read endpoint", False, str(e))
    
    def test_enhanced_calendar_endpoints(self):
        """Test enhanced calendar endpoints"""
        print("\n=== Testing Enhanced Calendar Endpoints ===")
        
        if not self.login_as_staff():
            self.log_test("Staff login for calendar tests", False, "Could not login as staff")
            return
        
        # Test enhanced staff attendance calendar
        try:
            today = date.today()
            start_date = (today - timedelta(days=30)).isoformat()
            end_date = (today + timedelta(days=30)).isoformat()
            
            params = {
                'start': start_date,
                'end': end_date
            }
            
            response = self.session.get(f"{self.base_url}/staff/attendance_calendar", params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # Check if enhanced fields are present
                    attendance_records = data.get('attendance', [])
                    if attendance_records:
                        first_record = attendance_records[0]
                        enhanced_fields = ['late_duration_minutes', 'early_departure_minutes', 'regularization_requested']
                        has_enhanced_fields = any(field in first_record for field in enhanced_fields)
                        
                        if has_enhanced_fields:
                            self.log_test("Enhanced staff attendance calendar endpoint", True, "Enhanced fields present")
                        else:
                            self.log_test("Enhanced staff attendance calendar endpoint", False, "Enhanced fields missing")
                    else:
                        self.log_test("Enhanced staff attendance calendar endpoint", True, "Endpoint works (no data)")
                else:
                    self.log_test("Enhanced staff attendance calendar endpoint", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Enhanced staff attendance calendar endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Enhanced staff attendance calendar endpoint", False, str(e))
        
        # Test enhanced get staff attendance
        try:
            params = {
                'start': start_date,
                'end': end_date
            }
            
            response = self.session.get(f"{self.base_url}/get_staff_attendance", params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Enhanced get staff attendance endpoint", True)
                else:
                    self.log_test("Enhanced get staff attendance endpoint", False, data.get('error', 'Unknown error'))
            else:
                self.log_test("Enhanced get staff attendance endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Enhanced get staff attendance endpoint", False, str(e))
    
    def test_server_availability(self):
        """Test if server is running"""
        print("\n=== Testing Server Availability ===")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("Server availability", True)
                return True
            else:
                self.log_test("Server availability", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Server availability", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting API Endpoints Test Suite")
        print("=" * 60)
        
        # Check server availability first
        if not self.test_server_availability():
            print("\n❌ Server is not available. Please start the Flask application first.")
            return False
        
        self.test_regularization_endpoints()
        self.test_staff_endpoints()
        self.test_enhanced_calendar_endpoints()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 API TEST SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['passed']]
        if failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['message']}")
        
        return passed_tests == total_tests


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test API endpoints for enhanced attendance system')
    parser.add_argument('--url', default='http://localhost:5000', help='Base URL of the Flask application')
    args = parser.parse_args()
    
    test_suite = APITestSuite(args.url)
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All API tests passed!")
        sys.exit(0)
    else:
        print("\n⚠️  Some API tests failed. Please check the server and implementation.")
        sys.exit(1)
