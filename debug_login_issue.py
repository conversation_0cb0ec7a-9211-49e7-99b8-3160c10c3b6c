#!/usr/bin/env python3
"""
Comprehensive debug for staff login issues
"""

import sqlite3
from werkzeug.security import check_password_hash
import requests
import json

def test_database_login(staff_id, password, school_id=4):
    """Test login directly against database"""
    
    print(f"=== Database Login Test for Staff ID: {staff_id} ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Exact same query as in Flask app
        cursor.execute('''
            SELECT * FROM staff 
            WHERE school_id = ? AND staff_id = ?
        ''', (school_id, staff_id))
        staff = cursor.fetchone()
        
        if not staff:
            print(f"❌ Staff not found with school_id={school_id} and staff_id={staff_id}")
            return False
        
        print(f"✅ Staff found: {staff['full_name']}")
        print(f"   Database ID: {staff['id']}")
        print(f"   Staff ID: {staff['staff_id']}")
        print(f"   School ID: {staff['school_id']}")
        print(f"   Email: {staff['email']}")
        
        password_hash = staff['password_hash'] if staff['password_hash'] is not None else ''
        
        if not password_hash:
            print("❌ No password hash found")
            return False
        
        print(f"✅ Password hash exists")
        
        # Test password verification
        if check_password_hash(password_hash, password):
            print(f"✅ Password verification PASSED")
            return True
        else:
            print(f"❌ Password verification FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        conn.close()

def test_web_login_detailed(staff_id, password, school_id=4):
    """Test web login with detailed debugging"""
    
    print(f"\n=== Web Login Test for Staff ID: {staff_id} ===")
    
    base_url = 'http://127.0.0.1:5000'
    
    try:
        session = requests.Session()
        
        # Step 1: Get main page
        print("Step 1: Getting main page...")
        main_response = session.get(base_url)
        print(f"   Status: {main_response.status_code}")
        
        if main_response.status_code != 200:
            print(f"❌ Failed to load main page")
            return False
        
        # Step 2: Attempt login
        print("Step 2: Attempting login...")
        login_data = {
            'school_id': str(school_id),
            'username': str(staff_id),
            'password': str(password)
        }
        
        print(f"   Login data: {login_data}")
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        print(f"   Response status: {login_response.status_code}")
        print(f"   Response headers: {dict(login_response.headers)}")
        
        if login_response.status_code != 200:
            print(f"❌ HTTP error: {login_response.status_code}")
            print(f"   Response text: {login_response.text}")
            return False
        
        try:
            response_json = login_response.json()
            print(f"   Response JSON: {response_json}")
            
            if 'redirect' in response_json:
                print(f"✅ Login SUCCESS - Redirect to: {response_json['redirect']}")
                return True
            elif 'error' in response_json:
                print(f"❌ Login FAILED - Error: {response_json['error']}")
                return False
            else:
                print(f"❌ Unexpected response format")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON response: {e}")
            print(f"   Raw response: {login_response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app")
        return False
    except Exception as e:
        print(f"❌ Web login error: {e}")
        return False

def check_all_staff_passwords():
    """Check and potentially reset all staff passwords"""
    
    print("\n=== Checking All Staff Passwords ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT id, staff_id, full_name, password_hash, school_id FROM staff')
        all_staff = cursor.fetchall()
        
        for staff in all_staff:
            print(f"\nStaff: {staff['full_name']} (ID: {staff['staff_id']})")
            
            if not staff['password_hash']:
                print("   ❌ No password set")
                continue
            
            # Test with default password
            if check_password_hash(staff['password_hash'], 'password123'):
                print("   ✅ Password is 'password123'")
            else:
                print("   ❌ Password is NOT 'password123'")
                
                # Try some common passwords
                common_passwords = ['123456', 'admin', 'password', staff['staff_id']]
                found = False
                for pwd in common_passwords:
                    if check_password_hash(staff['password_hash'], pwd):
                        print(f"   ✅ Password is '{pwd}'")
                        found = True
                        break
                
                if not found:
                    print("   ❓ Password is something else")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

def reset_all_passwords():
    """Reset all staff passwords to 'password123'"""
    
    print("\n=== Resetting All Staff Passwords ===")
    
    from werkzeug.security import generate_password_hash
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get all staff
        cursor.execute('SELECT id, staff_id, full_name FROM staff')
        all_staff = cursor.fetchall()
        
        new_password = 'password123'
        password_hash = generate_password_hash(new_password)
        
        for staff in all_staff:
            cursor.execute('UPDATE staff SET password_hash = ? WHERE id = ?', (password_hash, staff[0]))
            print(f"✅ Reset password for {staff[2]} (ID: {staff[1]})")
        
        conn.commit()
        print(f"\n🎉 All passwords reset to: {new_password}")
        
    except Exception as e:
        print(f"❌ Error resetting passwords: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== Staff Login Debug Tool ===")
    
    # Test all staff members
    staff_to_test = [
        ('888', 'Mohan'),
        ('333', 'Navanee'), 
        ('555', 'NN-555')
    ]
    
    print("\n1. Testing database login for all staff...")
    for staff_id, name in staff_to_test:
        print(f"\n--- Testing {name} (Staff ID: {staff_id}) ---")
        db_success = test_database_login(staff_id, 'password123')
        
        if db_success:
            print("Database login OK, testing web login...")
            web_success = test_web_login_detailed(staff_id, 'password123')
            
            if not web_success:
                print("❌ Web login failed even though database login works")
        else:
            print("❌ Database login failed")
    
    print("\n" + "="*60)
    print("2. Checking all staff passwords...")
    check_all_staff_passwords()
    
    print("\n" + "="*60)
    choice = input("Do you want to reset all passwords to 'password123'? (y/N): ").strip().lower()
    if choice == 'y':
        reset_all_passwords()
        
        print("\nTesting login after password reset...")
        for staff_id, name in staff_to_test:
            print(f"\n--- Re-testing {name} (Staff ID: {staff_id}) ---")
            test_database_login(staff_id, 'password123')
    
    print("\n" + "="*60)
    print("SUMMARY:")
    print("1. Try logging in with Staff ID and password 'password123'")
    print("2. Make sure you select the correct school (Bharathiyar)")
    print("3. Check browser console for any JavaScript errors")
    print("4. Clear browser cache and cookies if needed")
    print("5. If still failing, check Flask app logs for error messages")
