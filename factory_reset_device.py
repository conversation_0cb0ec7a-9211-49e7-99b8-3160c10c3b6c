#!/usr/bin/env python3
"""
ZK Device Factory Reset Tool
Attempt to reset device to factory defaults when web interface is not accessible
"""

import socket
import struct
import time
import sys

def try_factory_reset_via_protocol():
    """Attempt factory reset using ZK protocol commands"""
    print("🔄 Attempting factory reset via protocol...")
    print("-" * 40)
    
    try:
        # Try connecting on different ports
        ports = [32150, 4370, 8080]
        
        for port in ports:
            print(f"Trying port {port}...")
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect(("*************", port))
                
                # Send factory reset command sequence
                # This is a common ZK protocol reset sequence
                reset_commands = [
                    b'\x50\x50\x82\x7d\x13\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Connect
                    b'\x50\x50\x82\x7d\x14\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00',  # Reset command
                ]
                
                for cmd in reset_commands:
                    sock.send(cmd)
                    time.sleep(1)
                    try:
                        response = sock.recv(1024)
                        print(f"   Response: {len(response)} bytes")
                    except:
                        pass
                
                sock.close()
                print(f"✅ Reset command sent on port {port}")
                return True
                
            except Exception as e:
                print(f"   ❌ Port {port} failed: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ Protocol reset failed: {e}")
        return False

def try_default_configurations():
    """Try connecting with factory default configurations"""
    print("\n🔧 Testing factory default configurations...")
    print("-" * 40)
    
    # Common factory default settings
    default_configs = [
        {"ip": "*************", "port": 4370, "device_id": "1"},
        {"ip": "*************", "port": 4370, "device_id": "1"},
        {"ip": "*************", "port": 4370, "device_id": "1"},
        {"ip": "*************", "port": 32150, "device_id": "1"},
        {"ip": "*************", "port": 8080, "device_id": "1"},
    ]
    
    for i, config in enumerate(default_configs, 1):
        print(f"\nTesting default config {i}:")
        print(f"   IP: {config['ip']}")
        print(f"   Port: {config['port']}")
        print(f"   Device ID: {config['device_id']}")
        
        try:
            from zk_biometric import ZKBiometricDevice
            
            device = ZKBiometricDevice(
                device_ip=config['ip'],
                port=config['port'],
                timeout=5,
                device_id=config['device_id'],
                use_cloud=False
            )
            
            if device.connect():
                print("   ✅ SUCCESS! Factory defaults working")
                device.disconnect()
                return config
            else:
                print("   ❌ Failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

def provide_manual_reset_instructions():
    """Provide manual reset instructions"""
    print("\n📋 MANUAL FACTORY RESET INSTRUCTIONS")
    print("=" * 50)
    
    print("If software methods don't work, try these physical methods:")
    print()
    
    print("🔧 Method 1: Hardware Reset Button")
    print("   1. Locate the RESET button on the device (usually small, recessed)")
    print("   2. Power off the device")
    print("   3. Hold the RESET button while powering on")
    print("   4. Keep holding for 10-15 seconds")
    print("   5. Release and wait for device to boot")
    print()
    
    print("🔧 Method 2: Menu Reset (if device has display)")
    print("   1. Access device menu (usually by pressing MENU key)")
    print("   2. Navigate to System → Reset or Factory Reset")
    print("   3. Confirm the reset operation")
    print("   4. Wait for device to restart")
    print()
    
    print("🔧 Method 3: Jumper Reset (advanced)")
    print("   1. Power off and open device case")
    print("   2. Look for RESET or CLR jumper on circuit board")
    print("   3. Short the jumper pins while powering on")
    print("   4. Remove jumper after 10 seconds")
    print("   5. Close case and test")
    print()
    
    print("⚠️ After factory reset, default settings are usually:")
    print("   - IP Address: *************")
    print("   - Port: 4370")
    print("   - Device ID: 1")
    print("   - Admin Password: 123456")
    print("   - Web Interface: http://*************")

def test_after_reset():
    """Test device after reset"""
    print("\n🧪 Testing device after reset...")
    print("-" * 40)
    
    # Test common post-reset configurations
    test_configs = [
        {"ip": "*************", "port": 4370},
        {"ip": "*************", "port": 4370},
        {"ip": "*************", "port": 4370},
        {"ip": "*************", "port": 32150},
    ]
    
    for config in test_configs:
        print(f"Testing {config['ip']}:{config['port']}...", end=" ")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((config['ip'], config['port']))
            sock.close()
            
            if result == 0:
                print("✅ ACCESSIBLE")
                
                # Try ZK connection
                try:
                    from zk_biometric import ZKBiometricDevice
                    device = ZKBiometricDevice(
                        device_ip=config['ip'],
                        port=config['port'],
                        timeout=5,
                        device_id='1',
                        use_cloud=False
                    )
                    
                    if device.connect():
                        print(f"   🎯 ZK protocol working!")
                        device.disconnect()
                        return config
                    
                except:
                    pass
                    
            else:
                print("❌ Not accessible")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None

def main():
    """Main factory reset function"""
    print("🔄 ZK DEVICE FACTORY RESET TOOL")
    print("=" * 50)
    print("Device: *************")
    print("Attempting to reset to factory defaults")
    print("=" * 50)
    
    print("\n⚠️ WARNING: Factory reset will:")
    print("   - Erase all user data")
    print("   - Reset all settings to defaults")
    print("   - Clear attendance records")
    print()
    
    response = input("Continue with factory reset? (y/N): ").lower()
    if response != 'y':
        print("❌ Factory reset cancelled")
        return
    
    # Step 1: Try protocol-based reset
    print("\n🔄 Step 1: Protocol-based reset...")
    protocol_success = try_factory_reset_via_protocol()
    
    if protocol_success:
        print("✅ Protocol reset command sent")
        print("⏳ Waiting for device to restart...")
        time.sleep(30)  # Wait for restart
        
        # Test if reset worked
        reset_config = test_after_reset()
        if reset_config:
            print(f"\n🎉 SUCCESS! Device reset and accessible at:")
            print(f"   IP: {reset_config['ip']}")
            print(f"   Port: {reset_config['port']}")
            print(f"   Web Interface: http://{reset_config['ip']}")
            return
    
    # Step 2: Try default configurations
    print("\n🔧 Step 2: Testing factory defaults...")
    default_config = try_default_configurations()
    
    if default_config:
        print(f"\n✅ Device accessible with factory defaults:")
        print(f"   IP: {default_config['ip']}")
        print(f"   Port: {default_config['port']}")
        print(f"   Device ID: {default_config['device_id']}")
        return
    
    # Step 3: Manual instructions
    print("\n⚠️ Software reset methods failed")
    provide_manual_reset_instructions()
    
    print(f"\n🔄 After manual reset, run this tool again to test")

if __name__ == '__main__':
    main()
