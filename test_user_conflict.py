#!/usr/bin/env python3
"""
Test script to simulate and resolve user ID conflicts in biometric enrollment system.

This script helps test the "user already exists" issue resolution.
"""

import requests
import json
import sys

def test_user_conflict_resolution():
    """Test the user conflict resolution functionality"""
    
    base_url = "http://localhost:5000"  # Adjust if your app runs on different port
    
    print("🧪 Testing User Conflict Resolution System")
    print("=" * 50)
    
    # Test data
    test_user_id = "TEST123"
    test_name = "Test User"
    device_ip = "*************"
    
    print(f"📋 Test Parameters:")
    print(f"   User ID: {test_user_id}")
    print(f"   Name: {test_name}")
    print(f"   Device IP: {device_ip}")
    print()
    
    # Step 1: Try to enroll a user (this might fail if user already exists)
    print("1️⃣ Testing initial enrollment...")
    enroll_data = {
        'device_ip': device_ip,
        'user_id': test_user_id,
        'name': test_name,
        'privilege': 0,
        'overwrite': 'false'
    }
    
    try:
        response = requests.post(f"{base_url}/enroll_biometric_user", data=enroll_data)
        result = response.json()
        
        if result['success']:
            print("✅ User enrolled successfully!")
            print(f"   Message: {result['message']}")
        else:
            if result.get('user_exists'):
                print("⚠️ User already exists on device (expected for testing)")
                print(f"   Existing user: {result.get('existing_user', {})}")
                print(f"   Suggestion: {result.get('suggestion', 'No suggestion provided')}")
                
                # Step 2: Test conflict resolution - overwrite
                print("\n2️⃣ Testing conflict resolution - overwrite...")
                resolve_data = {
                    'action': 'overwrite',
                    'device_ip': device_ip,
                    'user_id': test_user_id,
                    'name': test_name,
                    'privilege': 0
                }
                
                resolve_response = requests.post(f"{base_url}/resolve_user_conflict", data=resolve_data)
                resolve_result = resolve_response.json()
                
                if resolve_result['success']:
                    print("✅ Conflict resolved by overwriting!")
                    print(f"   Message: {resolve_result['message']}")
                else:
                    print("❌ Failed to resolve conflict by overwriting")
                    print(f"   Error: {resolve_result.get('message', 'Unknown error')}")
                
                # Step 3: Test creating staff from existing user
                print("\n3️⃣ Testing staff creation from existing biometric user...")
                staff_data = {
                    'action': 'create_from_existing',
                    'existing_user_id': test_user_id,
                    'full_name': test_name,
                    'password': 'test123',
                    'email': '<EMAIL>',
                    'phone': '**********',
                    'department': 'Testing',
                    'position': 'Test User'
                }
                
                staff_response = requests.post(f"{base_url}/resolve_user_conflict", data=staff_data)
                staff_result = staff_response.json()
                
                if staff_result['success']:
                    print("✅ Staff account created from existing biometric user!")
                    print(f"   Message: {staff_result['message']}")
                else:
                    print("❌ Failed to create staff from existing user")
                    print(f"   Error: {staff_result.get('message', 'Unknown error')}")
                    
            else:
                print("❌ Enrollment failed for other reason")
                print(f"   Error: {result.get('message', 'Unknown error')}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the application")
        print("   Make sure the Flask app is running on http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("   The system now provides better user conflict resolution:")
    print("   • Clear error messages when user already exists")
    print("   • Multiple resolution options (overwrite, different ID, create from existing)")
    print("   • User-friendly modal interface instead of confusing prompts")
    print("   • Proper integration with existing enrollment workflow")
    
    return True

def check_biometric_device_connection():
    """Check if biometric device is accessible"""
    print("🔌 Checking biometric device connection...")
    
    base_url = "http://localhost:5000"
    device_ip = "*************"
    
    try:
        response = requests.post(f"{base_url}/check_biometric_user", data={
            'device_ip': device_ip,
            'user_id': 'TEST_CONNECTION'
        })
        
        result = response.json()
        
        if result['success'] or 'connect' in result.get('message', '').lower():
            print("✅ Biometric device connection test completed")
            return True
        else:
            print("⚠️ Biometric device may not be accessible")
            print(f"   Message: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Device connection test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Biometric User Conflict Resolution Test")
    print("This script tests the solution for 'user already exists' issues")
    print()
    
    # Check device connection first
    device_ok = check_biometric_device_connection()
    print()
    
    if device_ok or input("Continue with tests anyway? (y/n): ").lower() == 'y':
        success = test_user_conflict_resolution()
        
        if success:
            print("\n✅ All tests completed!")
            print("\n📖 How to use the solution:")
            print("1. When adding a new staff member, if you get 'user already exists' error:")
            print("2. A modal will appear with 3 options:")
            print("   • Overwrite existing user (replaces biometric data)")
            print("   • Use different ID (choose a new staff ID)")
            print("   • Create from existing (make staff account for existing biometric user)")
            print("3. Choose the appropriate option based on your situation")
            print("4. The system will handle the rest automatically")
        else:
            print("\n❌ Tests failed. Check the error messages above.")
            sys.exit(1)
    else:
        print("❌ Skipping tests due to device connection issues")
        sys.exit(1)
