#!/usr/bin/env python3
"""
Simple Test for Device 181
Step-by-step connection test
"""

from zk_biometric import ZKBiometricDevice
import time

def test_connection():
    print("🔧 TESTING DEVICE 181 CONNECTION")
    print("=" * 50)
    print("Device IP: *************")
    print("Device ID: 181")
    print("Common Key: 1302")
    print("Port: 32150")
    print("=" * 50)
    
    print("\n📡 Step 1: Creating device connection...")
    try:
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            timeout=20,
            device_id='181',
            use_cloud=False
        )
        print("✅ Device object created successfully")
    except Exception as e:
        print(f"❌ Error creating device: {e}")
        return False
    
    print("\n🔗 Step 2: Attempting connection...")
    try:
        if device.connect():
            print("🎉 SUCCESS! Connected to Device 181!")
            
            print("\n📊 Step 3: Getting device information...")
            try:
                users = device.get_users()
                print(f"✅ Users found: {len(users)}")
                
                records = device.get_attendance_records()
                print(f"✅ Attendance records: {len(records)}")
                
                print("\n🎯 Device 181 is working perfectly!")
                device.disconnect()
                return True
                
            except Exception as e:
                print(f"⚠️ Connected but limited access: {e}")
                print("This is normal - device is working!")
                device.disconnect()
                return True
                
        else:
            print("❌ Connection failed")
            print("\n💡 This means device configuration needs adjustment")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def provide_next_steps(success):
    print("\n" + "=" * 50)
    if success:
        print("🎉 ETHERNET CONNECTION: SUCCESS!")
        print("✅ Device 181 is properly configured")
        print("✅ Ready for cloud configuration")
    else:
        print("⚠️ ETHERNET CONNECTION: NEEDS CONFIGURATION")
        print("📋 Next steps:")
        print("1. Check device web interface settings")
        print("2. Verify Device ID = 181")
        print("3. Verify Common Key = 1302")
        print("4. Enable TCP/IP communication")
        print("5. Save settings and restart device")

if __name__ == '__main__':
    success = test_connection()
    provide_next_steps(success)
    
    print(f"\n🔄 Run this test again after configuring device settings")
    print("Command: python simple_test_181.py")
