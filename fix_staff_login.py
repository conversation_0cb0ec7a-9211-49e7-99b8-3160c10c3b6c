#!/usr/bin/env python3
"""
Script to fix staff login issues by setting default passwords
"""

import sqlite3
from werkzeug.security import generate_password_hash

def fix_staff_passwords():
    """Set default passwords for staff members who don't have passwords"""
    
    # Connect to database
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Find staff without passwords
        cursor.execute('''
            SELECT id, staff_id, full_name, school_id, password_hash 
            FROM staff 
            WHERE password_hash IS NULL OR password_hash = ''
        ''')
        staff_without_passwords = cursor.fetchall()
        
        if not staff_without_passwords:
            print("✅ All staff members already have passwords set.")
            return
        
        print(f"Found {len(staff_without_passwords)} staff members without passwords:")
        for staff in staff_without_passwords:
            print(f"  - ID: {staff['id']}, Staff ID: {staff['staff_id']}, Name: {staff['full_name']}")
        
        # Set default password
        default_password = 'password123'
        password_hash = generate_password_hash(default_password)
        
        # Update staff passwords
        updated_count = 0
        for staff in staff_without_passwords:
            cursor.execute('''
                UPDATE staff SET password_hash = ? WHERE id = ?
            ''', (password_hash, staff['id']))
            updated_count += 1
            print(f"✅ Set password for {staff['full_name']} (Staff ID: {staff['staff_id']})")
        
        conn.commit()
        print(f"\n🎉 Successfully set default password for {updated_count} staff members.")
        print(f"Default password: {default_password}")
        print("\nStaff can now login using:")
        print("- Username: Their Staff ID")
        print(f"- Password: {default_password}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        conn.close()

def test_staff_login():
    """Test staff login functionality"""
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a sample staff member
        cursor.execute('''
            SELECT id, staff_id, full_name, school_id, password_hash 
            FROM staff 
            WHERE password_hash IS NOT NULL AND password_hash != ''
            LIMIT 1
        ''')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff with passwords found. Run fix_staff_passwords() first.")
            return
        
        print(f"Testing login for: {staff['full_name']} (Staff ID: {staff['staff_id']})")
        print(f"School ID: {staff['school_id']}")
        print(f"Has password hash: {'Yes' if staff['password_hash'] else 'No'}")
        
        # Test password verification
        from werkzeug.security import check_password_hash
        test_password = 'password123'
        
        if check_password_hash(staff['password_hash'], test_password):
            print(f"✅ Password verification successful for '{test_password}'")
        else:
            print(f"❌ Password verification failed for '{test_password}'")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== Staff Login Fix Script ===\n")
    
    print("1. Fixing staff passwords...")
    fix_staff_passwords()
    
    print("\n2. Testing staff login...")
    test_staff_login()
    
    print("\n=== Instructions ===")
    print("1. Start your Flask app: python app.py")
    print("2. Go to the login page")
    print("3. Select a school")
    print("4. Use Staff ID as username and 'password123' as password")
    print("5. Staff should now be able to login successfully")
