{"config": {"cloud_provider": "custom", "api_base_url": "http://*************:32150", "websocket_url": "ws://*************:32150", "mqtt_broker": "*************", "mqtt_port": 32150, "api_key": "gAAAAABodKGOjkLOiVZLeF4F6EEhZ3cMPLEb9c5HidNxxfrt63pqDwtUwlgZ2A3NXkmYtXaJ5WF6uksvMptDbwDWIEFQxQgtV9lxvzTyhwOqw68hNFaP60I=", "secret_key": "gAAAAABodKGOkA87ByBJdALbX39ObU49qcgbK_hoTWXBepyGmML_8v2CbKiTHpj88EuW0ajp0u2T2mb89M6JJ-kuGIL0_llYM9vn-NniCumgDcwdMPl9-EI=", "organization_id": "your_organization_id", "connection_timeout": 30, "retry_attempts": 3, "heartbeat_interval": 60, "use_ssl": false, "verify_ssl": false, "encryption_enabled": true, "auto_sync": true, "sync_interval": 30, "batch_size": 100, "local_backup": true, "backup_retention_days": 30}, "devices": [{"device_id": "181", "device_name": "ZK Biometric Device 181", "device_type": "ZK_BIOMETRIC", "local_ip": "*************", "local_port": 32150, "cloud_enabled": true, "sync_interval": 30, "last_sync": null}], "endpoints": [{"name": "primary", "url": "http://*************:32150", "api_key": "gAAAAABodKGOUYutSVcBBXgVTFAcHZq4jHS5zB1jZX2LxOsNrg61fobkvFz94fkLLnR4gpqwHn4Dluwm2wjqHQV2VoPRqHKI3g7LMGwsznLbL4zS4lwFZtU=", "timeout": 30, "retry_attempts": 3, "enabled": true}]}