#!/usr/bin/env python3
"""
Create Test Verification Records

This script creates sample biometric verification records for today
to test the Today's Verification History functionality.
"""

import sqlite3
import os
from datetime import datetime, timed<PERSON>ta


def create_test_verifications():
    """Create test verification records for today"""
    
    if not os.path.exists('vishnorex.db'):
        print("❌ Database file 'vishnorex.db' not found!")
        return False
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get current datetime
        now = datetime.now()
        
        # Create test verification records for today
        test_verifications = [
            {
                'staff_id': 20,
                'school_id': 1,
                'verification_type': 'check-in',
                'verification_time': now.replace(hour=9, minute=25, second=30),
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_status': 'success',
                'notes': 'Test verification - morning check-in'
            },
            {
                'staff_id': 20,
                'school_id': 1,
                'verification_type': 'check-out',
                'verification_time': now.replace(hour=16, minute=35, second=15),
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_status': 'success',
                'notes': 'Test verification - evening check-out'
            },
            {
                'staff_id': 20,
                'school_id': 1,
                'verification_type': 'overtime-in',
                'verification_time': now.replace(hour=17, minute=5, second=45),
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_status': 'success',
                'notes': 'Test verification - overtime start'
            },
            {
                'staff_id': 13,  # Different staff member
                'school_id': 1,
                'verification_type': 'check-in',
                'verification_time': now.replace(hour=9, minute=18, second=22),
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_status': 'success',
                'notes': 'Test verification - staff 13 check-in'
            },
            {
                'staff_id': 13,
                'school_id': 1,
                'verification_type': 'check-out',
                'verification_time': now.replace(hour=16, minute=28, second=55),
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_status': 'success',
                'notes': 'Test verification - staff 13 check-out'
            }
        ]
        
        print("🔧 Creating test verification records for today...")
        print("=" * 50)
        
        # Insert test records
        for i, verification in enumerate(test_verifications, 1):
            cursor.execute('''
                INSERT INTO biometric_verifications 
                (staff_id, school_id, verification_type, verification_time, device_ip, 
                 biometric_method, verification_status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                verification['staff_id'],
                verification['school_id'],
                verification['verification_type'],
                verification['verification_time'],
                verification['device_ip'],
                verification['biometric_method'],
                verification['verification_status'],
                verification['notes']
            ))
            
            print(f"✅ Created verification {i}: Staff {verification['staff_id']} - {verification['verification_type']} at {verification['verification_time'].strftime('%I:%M %p')}")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 Successfully created {len(test_verifications)} test verification records!")
        
        # Verify the records were created
        today = now.date()
        cursor.execute('''
            SELECT COUNT(*) FROM biometric_verifications 
            WHERE DATE(verification_time) = ?
        ''', (today,))
        
        today_count = cursor.fetchone()[0]
        print(f"📊 Total verifications for today: {today_count}")
        
        # Show today's records
        cursor.execute('''
            SELECT staff_id, verification_type, verification_time, verification_status
            FROM biometric_verifications 
            WHERE DATE(verification_time) = ?
            ORDER BY verification_time
        ''', (today,))
        
        today_records = cursor.fetchall()
        print(f"\n📋 Today's verification records:")
        for record in today_records:
            staff_id, v_type, v_time, v_status = record
            dt = datetime.fromisoformat(v_time.replace('Z', '+00:00'))
            time_str = dt.strftime('%I:%M %p')
            print(f"  - Staff {staff_id}: {v_type} at {time_str} ({v_status})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test verifications: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()


def main():
    """Main function"""
    print("🧪 VishnoRex Test Verification Creator")
    print("=" * 60)
    print("Creating sample biometric verification records for today")
    print("This will allow testing of the Today's Verification History function")
    print()
    
    # Confirm creation
    confirm = input("Do you want to create test verification records for today? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ Test record creation cancelled by user")
        return
    
    # Create test records
    success = create_test_verifications()
    
    if success:
        print("\n🚀 Test records created successfully!")
        print("📝 You can now test the Today's Verification History function")
        print("🌐 Visit the staff dashboard to see the verification history")
        print("\n💡 To test:")
        print("1. Login as staff member (ID 20 or 13)")
        print("2. Check the 'Today's Verification History' section")
        print("3. Verify that the records appear correctly")
    else:
        print("\n💥 Failed to create test records!")
        print("📝 Please check the error messages above")


if __name__ == "__main__":
    main()
