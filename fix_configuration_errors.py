#!/usr/bin/env python3
"""
Fix Configuration Errors
Fix WebSocket scheme errors and device IP configuration issues
"""

import json
import os
import sys

def fix_cloud_config():
    """Fix cloud configuration issues"""
    print("🔧 Fixing Cloud Configuration")
    print("-" * 40)
    
    config_file = "cloud_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ {config_file} not found")
        return False
    
    try:
        # Read current configuration
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print("📋 Current configuration issues found:")
        
        # Fix WebSocket URL scheme
        if 'config' in config:
            websocket_url = config['config'].get('websocket_url', '')
            if websocket_url.startswith('https://'):
                print("   ❌ WebSocket URL uses HTTPS (should be WSS)")
                config['config']['websocket_url'] = websocket_url.replace('https://', 'wss://')
                print(f"   ✅ Fixed: {config['config']['websocket_url']}")
            elif websocket_url.startswith('http://'):
                print("   ⚠️ WebSocket URL uses HTTP (changing to WS)")
                config['config']['websocket_url'] = websocket_url.replace('http://', 'ws://')
                print(f"   ✅ Fixed: {config['config']['websocket_url']}")
            
            # Ensure SSL is disabled for WS connections
            if config['config']['websocket_url'].startswith('ws://'):
                config['config']['use_ssl'] = False
                config['config']['verify_ssl'] = False
                print("   ✅ Disabled SSL for WS connection")
        
        # Fix device IP addresses
        if 'devices' in config:
            for i, device in enumerate(config['devices']):
                old_ip = device.get('local_ip', '')
                if old_ip == '************':
                    print(f"   ❌ Device {device.get('device_id', i)} has wrong IP: {old_ip}")
                    
                    # Update to correct IP based on our findings
                    if device.get('device_id') == '181':
                        device['local_ip'] = '*************'
                        device['local_port'] = 32150
                        print(f"   ✅ Updated to: {device['local_ip']}:{device['local_port']}")
                    else:
                        device['local_ip'] = '*************'
                        device['local_port'] = 4370
                        print(f"   ✅ Updated to factory default: {device['local_ip']}:{device['local_port']}")
        
        # Save fixed configuration
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Cloud configuration fixed and saved")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing cloud config: {e}")
        return False

def create_ethernet_only_config():
    """Create a clean Ethernet-only configuration"""
    print("\n🔌 Creating Ethernet-Only Configuration")
    print("-" * 40)
    
    ethernet_config = {
        "config": {
            "cloud_provider": "disabled",
            "api_base_url": "",
            "websocket_url": "",
            "mqtt_broker": "",
            "mqtt_port": 0,
            "api_key": "",
            "secret_key": "",
            "organization_id": "",
            "connection_timeout": 30,
            "retry_attempts": 3,
            "heartbeat_interval": 60,
            "use_ssl": False,
            "verify_ssl": False,
            "encryption_enabled": False,
            "auto_sync": False,
            "sync_interval": 30,
            "batch_size": 100,
            "local_backup": True,
            "backup_retention_days": 30
        },
        "devices": [
            {
                "device_id": "181",
                "device_name": "ZK Biometric Device 181",
                "device_type": "ZK_BIOMETRIC",
                "local_ip": "*************",
                "local_port": 32150,
                "cloud_enabled": False,
                "sync_interval": 30,
                "last_sync": None
            },
            {
                "device_id": "1",
                "device_name": "ZK Factory Default Device",
                "device_type": "ZK_BIOMETRIC",
                "local_ip": "*************",
                "local_port": 4370,
                "cloud_enabled": False,
                "sync_interval": 30,
                "last_sync": None
            }
        ],
        "endpoints": []
    }
    
    try:
        with open('cloud_config_ethernet_only.json', 'w') as f:
            json.dump(ethernet_config, f, indent=2)
        
        print("✅ Ethernet-only configuration created: cloud_config_ethernet_only.json")
        print("📋 This configuration:")
        print("   - Disables all cloud functionality")
        print("   - Configures device 181 at *************:32150")
        print("   - Includes factory default device at *************:4370")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating ethernet config: {e}")
        return False

def test_device_connectivity():
    """Test connectivity to configured devices"""
    print("\n🧪 Testing Device Connectivity")
    print("-" * 40)
    
    devices_to_test = [
        {"ip": "*************", "port": 32150, "name": "Primary Device"},
        {"ip": "*************", "port": 4370, "name": "Factory Default"},
        {"ip": "*************", "port": 80, "name": "Web Interface"}
    ]
    
    import socket
    
    working_devices = []
    
    for device in devices_to_test:
        try:
            print(f"Testing {device['name']} ({device['ip']}:{device['port']})...", end=" ")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((device['ip'], device['port']))
            sock.close()
            
            if result == 0:
                print("✅ ACCESSIBLE")
                working_devices.append(device)
            else:
                print("❌ Not accessible")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    if working_devices:
        print(f"\n✅ Found {len(working_devices)} accessible device(s)")
        for device in working_devices:
            print(f"   - {device['name']}: {device['ip']}:{device['port']}")
    else:
        print("\n❌ No devices accessible")
    
    return working_devices

def provide_next_steps():
    """Provide next steps based on configuration fixes"""
    print("\n📋 NEXT STEPS")
    print("=" * 40)
    
    print("1. 🔄 Restart your Flask application:")
    print("   - Stop the current application (Ctrl+C)")
    print("   - Run: python app.py")
    print()
    
    print("2. 🧪 Test device connection:")
    print("   - Run: python simple_test_181.py")
    print("   - Or use the web interface to test biometric connection")
    print()
    
    print("3. 🌐 If using web interface:")
    print("   - Go to Admin Dashboard")
    print("   - Test biometric connection")
    print("   - Should now connect without WebSocket errors")
    print()
    
    print("4. 🔧 If still having issues:")
    print("   - Use ethernet-only config: copy cloud_config_ethernet_only.json to cloud_config.json")
    print("   - This disables all cloud functionality")
    print("   - Focuses only on direct Ethernet connection")

def main():
    """Main configuration fix function"""
    print("🔧 CONFIGURATION ERROR FIX TOOL")
    print("=" * 50)
    print("Fixing WebSocket scheme errors and device IP issues")
    print("=" * 50)
    
    # Fix cloud configuration
    if fix_cloud_config():
        print("✅ Cloud configuration fixed")
    else:
        print("❌ Could not fix cloud configuration")
    
    # Create ethernet-only alternative
    if create_ethernet_only_config():
        print("✅ Ethernet-only configuration created")
    
    # Test device connectivity
    working_devices = test_device_connectivity()
    
    # Provide next steps
    provide_next_steps()
    
    print("\n🎯 SUMMARY:")
    print("✅ Fixed WebSocket scheme errors")
    print("✅ Updated device IP addresses")
    print("✅ Created ethernet-only configuration option")
    print(f"✅ Found {len(working_devices)} accessible devices")
    
    print("\n⚠️ IMPORTANT:")
    print("Restart your Flask application for changes to take effect!")

if __name__ == '__main__':
    main()
