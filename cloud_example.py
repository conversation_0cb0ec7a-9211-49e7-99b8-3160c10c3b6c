#!/usr/bin/env python3
"""
ZK Biometric Cloud Example
Demonstrates how to use the cloud-enabled ZK biometric system
"""

import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_ethernet_connection():
    """Example: Connect to ZK device via Ethernet (Legacy mode)"""
    print("\n" + "="*50)
    print("ETHERNET CONNECTION EXAMPLE")
    print("="*50)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Create device instance for Ethernet connection
        device = ZKBiometricDevice(
            device_ip='*************',
            port=32150,
            use_cloud=False  # Force Ethernet mode
        )

        print(f"Connecting to device at *************:32150...")
        
        if device.connect():
            print("✅ Connected successfully via Ethernet!")
            
            # Get users
            users = device.get_users()
            print(f"📱 Found {len(users)} users on device")
            
            # Get attendance records
            records = device.get_attendance_records()
            print(f"📊 Found {len(records)} attendance records")
            
            # Show recent records
            if records:
                print("\n📋 Recent attendance records:")
                for record in records[-5:]:  # Show last 5 records
                    print(f"  - User {record['user_id']}: {record['verification_type']} at {record['timestamp']}")
            
            device.disconnect()
            print("✅ Disconnected successfully")
            
        else:
            print("❌ Failed to connect via Ethernet")
            print("💡 Make sure device is powered on and network is configured")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_cloud_connection():
    """Example: Connect to ZK device via Cloud"""
    print("\n" + "="*50)
    print("CLOUD CONNECTION EXAMPLE")
    print("="*50)
    
    try:
        from zk_biometric import ZKBiometricDevice
        from cloud_connector import get_cloud_connector
        from cloud_config import get_cloud_config
        
        # Check cloud configuration
        config = get_cloud_config()
        if not config.api_key:
            print("❌ Cloud not configured. Please run: python network_config.py")
            return
        
        print(f"🌐 Cloud API: {config.api_base_url}")
        print(f"🏢 Organization: {config.organization_id}")
        
        # Create device instance for Cloud connection
        device = ZKBiometricDevice(
            device_id='ZK_001',  # Use device ID for cloud
            use_cloud=True  # Force cloud mode
        )
        
        print(f"Connecting to device ZK_001 via cloud...")
        
        if device.connect():
            print("✅ Connected successfully via Cloud!")
            
            # Get users
            users = device.get_users()
            print(f"📱 Found {len(users)} users on device")
            
            # Get attendance records
            records = device.get_attendance_records()
            print(f"📊 Found {len(records)} attendance records")
            
            # Show recent records
            if records:
                print("\n📋 Recent attendance records:")
                for record in records[-5:]:  # Show last 5 records
                    print(f"  - User {record['user_id']}: {record['verification_type']} at {record['timestamp']}")
            
            device.disconnect()
            print("✅ Disconnected successfully")
            
        else:
            print("❌ Failed to connect via Cloud")
            print("💡 Check cloud connector status and device configuration")
            
    except ImportError:
        print("❌ Cloud modules not available")
        print("💡 Install cloud dependencies: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Error: {e}")

def example_auto_connection():
    """Example: Auto-detect best connection method"""
    print("\n" + "="*50)
    print("AUTO-DETECT CONNECTION EXAMPLE")
    print("="*50)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Create device instance with auto-detection
        device = ZKBiometricDevice(
            device_ip='*************',  # Ethernet fallback
            device_id='ZK_001'  # Cloud device ID
            # use_cloud=None (default) enables auto-detection
        )
        
        print(f"Auto-detecting best connection method...")
        print(f"Connection mode: {'Cloud' if device.use_cloud else 'Ethernet'}")
        
        if device.connect():
            connection_type = "Cloud" if device.use_cloud else "Ethernet"
            print(f"✅ Connected successfully via {connection_type}!")
            
            # Get device info
            users = device.get_users()
            print(f"📱 Found {len(users)} users on device")
            
            device.disconnect()
            print("✅ Disconnected successfully")
            
        else:
            print("❌ Failed to connect with any method")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_cloud_api():
    """Example: Use Cloud API directly"""
    print("\n" + "="*50)
    print("CLOUD API EXAMPLE")
    print("="*50)
    
    try:
        from cloud_connector import get_cloud_connector
        
        connector = get_cloud_connector()
        
        if not connector.running:
            print("❌ Cloud connector is not running")
            print("💡 Start the Flask application to enable cloud connector")
            return
        
        print("✅ Cloud connector is running")
        
        # Get device status
        device_status = connector.get_device_status('ZK_001')
        print(f"📱 Device ZK_001 status: {device_status['status']}")
        
        # Make API request
        response = connector.send_cloud_api_request('devices', method='GET')
        if response and response.get('success'):
            devices = response.get('devices', [])
            print(f"🌐 Found {len(devices)} devices in cloud")
            
            for device in devices:
                print(f"  - {device['device_id']}: {device['device_name']} ({device['status']['status']})")
        else:
            print("❌ Failed to get devices from cloud API")
            
    except ImportError:
        print("❌ Cloud modules not available")
    except Exception as e:
        print(f"❌ Error: {e}")

def example_user_enrollment():
    """Example: Enroll a user on the device"""
    print("\n" + "="*50)
    print("USER ENROLLMENT EXAMPLE")
    print("="*50)
    
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Use auto-detection for best connection
        device = ZKBiometricDevice(
            device_ip='*************',
            device_id='ZK_001'
        )
        
        if device.connect():
            connection_type = "Cloud" if device.use_cloud else "Ethernet"
            print(f"✅ Connected via {connection_type}")
            
            # Enroll a test user
            user_id = "TEST001"
            name = "Test User"
            
            print(f"👤 Enrolling user {user_id} ({name})...")
            
            result = device.enroll_user(
                user_id=user_id,
                name=name,
                privilege=0,  # Normal user
                overwrite=True  # Overwrite if exists
            )
            
            if result['success']:
                print(f"✅ User enrolled successfully: {result['message']}")
                print("💡 Please use the device to capture biometric data")
            else:
                print(f"❌ Enrollment failed: {result['message']}")
            
            device.disconnect()
            
        else:
            print("❌ Failed to connect to device")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_cloud_status():
    """Example: Check cloud system status"""
    print("\n" + "="*50)
    print("CLOUD STATUS EXAMPLE")
    print("="*50)
    
    try:
        from cloud_config import get_cloud_config, get_all_devices
        from cloud_connector import get_cloud_connector
        
        # Check configuration
        config = get_cloud_config()
        print(f"🌐 API Base URL: {config.api_base_url}")
        print(f"🔒 SSL Enabled: {config.use_ssl}")
        print(f"🔄 Auto Sync: {config.auto_sync}")
        print(f"⏱️  Sync Interval: {config.sync_interval}s")
        print(f"🏢 Organization: {config.organization_id}")
        print(f"🔑 API Key: {'✅ Set' if config.api_key else '❌ Not set'}")
        
        # Check devices
        devices = get_all_devices()
        print(f"\n📱 Configured Devices: {len(devices)}")
        for device in devices:
            status = "🟢 Enabled" if device.cloud_enabled else "🔴 Disabled"
            print(f"  - {device.device_id}: {device.device_name} ({status})")
        
        # Check connector
        connector = get_cloud_connector()
        print(f"\n🔗 Cloud Connector: {'🟢 Running' if connector.running else '🔴 Stopped'}")
        
        if connector.running:
            print(f"📡 WebSocket: {'🟢 Connected' if connector.websocket else '🔴 Disconnected'}")
            print(f"💬 Message Queue: {len(connector.message_queue)} pending")
            print(f"💓 Last Heartbeat: {connector.last_heartbeat or 'Never'}")
        
    except ImportError:
        print("❌ Cloud modules not available")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main example function"""
    print("ZK Biometric Cloud System Examples")
    print("=" * 60)
    
    print("\nAvailable examples:")
    print("1. Ethernet Connection (Legacy)")
    print("2. Cloud Connection")
    print("3. Auto-detect Connection")
    print("4. Cloud API Usage")
    print("5. User Enrollment")
    print("6. Cloud Status Check")
    print("7. Run All Examples")
    
    try:
        choice = input("\nEnter your choice (1-7): ").strip()
        
        if choice == '1':
            example_ethernet_connection()
        elif choice == '2':
            example_cloud_connection()
        elif choice == '3':
            example_auto_connection()
        elif choice == '4':
            example_cloud_api()
        elif choice == '5':
            example_user_enrollment()
        elif choice == '6':
            example_cloud_status()
        elif choice == '7':
            print("\n🚀 Running all examples...")
            example_cloud_status()
            example_ethernet_connection()
            example_cloud_connection()
            example_auto_connection()
            example_cloud_api()
            # Skip user enrollment in batch mode
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == '__main__':
    main()
