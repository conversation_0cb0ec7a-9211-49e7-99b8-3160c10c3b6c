#!/usr/bin/env python3
"""
Raw Socket ZK Device Configuration
Direct socket communication to configure ZK device without web interface
"""

import socket
import struct
import time
import binascii

class RawZKConfig:
    """Raw socket communication with ZK device"""
    
    def __init__(self, ip="*************", port=32150):
        self.ip = ip
        self.port = port
        self.socket = None
        
    def test_raw_connection(self):
        """Test raw socket connection"""
        print(f"🔌 Testing raw socket connection to {self.ip}:{self.port}")
        
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.ip, self.port))
            
            print("✅ Socket connected successfully")
            
            # Send a basic probe
            probe_data = b"GET / HTTP/1.1\r\nHost: " + self.ip.encode() + b"\r\n\r\n"
            self.socket.send(probe_data)
            
            response = self.socket.recv(1024)
            print(f"📡 Received {len(response)} bytes")
            
            if response:
                # Check if it's HTTP response
                if b"HTTP" in response:
                    print("🌐 Device responds to HTTP requests")
                    return "HTTP"
                elif len(response) > 0:
                    print(f"📦 Raw response: {response[:50]}...")
                    return "RAW"
            
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
        finally:
            if self.socket:
                self.socket.close()
    
    def test_zk_protocol_handshake(self):
        """Test ZK protocol handshake"""
        print(f"\n🤝 Testing ZK protocol handshake...")
        
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.ip, self.port))
            
            # ZK protocol connection command
            # Format: [START][CMD][CHKSUM][SESSION][REPLY][SIZE][DATA]
            connect_cmd = struct.pack('<HHHHHH', 
                                    0x5050,  # Start marker
                                    1000,    # CMD_CONNECT
                                    0,       # Checksum (calculated later)
                                    0,       # Session ID
                                    1,       # Reply ID
                                    0)       # Data size
            
            # Calculate checksum
            checksum = sum(connect_cmd[8:]) % 65536
            connect_cmd = connect_cmd[:4] + struct.pack('<H', checksum) + connect_cmd[6:]
            
            print(f"📤 Sending ZK connect command: {binascii.hexlify(connect_cmd)}")
            self.socket.send(connect_cmd)
            
            response = self.socket.recv(1024)
            print(f"📥 Received response: {len(response)} bytes")
            
            if len(response) >= 8:
                start, cmd, chksum, session = struct.unpack('<HHHH', response[:8])
                print(f"   Start: 0x{start:04x}")
                print(f"   Command: {cmd}")
                print(f"   Session: {session}")
                
                if start == 0x5050:
                    print("✅ Valid ZK protocol response!")
                    return session
            
            return False
            
        except Exception as e:
            print(f"❌ ZK handshake failed: {e}")
            return False
        finally:
            if self.socket:
                self.socket.close()

def test_multiple_ports():
    """Test multiple ports for ZK communication"""
    print("🔍 TESTING MULTIPLE PORTS FOR ZK COMMUNICATION")
    print("=" * 60)
    
    ports_to_test = [32150, 4370, 80, 443, 8080, 8000, 9000]
    
    for port in ports_to_test:
        print(f"\n📡 Testing port {port}...")
        print("-" * 30)
        
        config = RawZKConfig("*************", port)
        
        # Test basic connection
        conn_result = config.test_raw_connection()
        
        if conn_result:
            # Test ZK protocol
            zk_result = config.test_zk_protocol_handshake()
            
            if zk_result:
                print(f"🎯 FOUND WORKING ZK PORT: {port}")
                return port
        
        time.sleep(1)  # Brief pause between tests
    
    return None

def try_telnet_configuration():
    """Try to configure via telnet if available"""
    print(f"\n📞 Testing Telnet Configuration Access...")
    print("-" * 40)
    
    try:
        import telnetlib
        
        # Try telnet on common ports
        telnet_ports = [23, 2323, 8023]
        
        for port in telnet_ports:
            try:
                print(f"Trying telnet on port {port}...", end=" ")
                tn = telnetlib.Telnet("*************", port, timeout=5)
                
                # Send some common commands
                tn.write(b"\r\n")
                response = tn.read_very_eager()
                
                if response:
                    print("✅ Telnet accessible!")
                    print(f"Response: {response.decode('utf-8', errors='ignore')[:100]}")
                    tn.close()
                    return port
                
                tn.close()
                print("❌ No response")
                
            except Exception as e:
                print(f"❌ Failed: {e}")
        
        return None
        
    except ImportError:
        print("❌ Telnetlib not available")
        return None

def try_snmp_configuration():
    """Try SNMP configuration if available"""
    print(f"\n📊 Testing SNMP Configuration Access...")
    print("-" * 40)
    
    try:
        # Simple SNMP test using socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        
        # Basic SNMP GET request for system description
        snmp_request = binascii.unhexlify("30820100020103300f02020000020300ffe30401040201030410300e0400020100020100040004000400301e040004000400301004000400040004000400040004000400")
        
        sock.sendto(snmp_request, ("*************", 161))
        response, addr = sock.recvfrom(1024)
        
        if response:
            print("✅ SNMP accessible!")
            print(f"Response length: {len(response)} bytes")
            return True
            
    except Exception as e:
        print(f"❌ SNMP failed: {e}")
        return False

def provide_alternative_solutions():
    """Provide alternative configuration solutions"""
    print(f"\n💡 ALTERNATIVE CONFIGURATION METHODS")
    print("=" * 50)
    
    print("Since direct protocol configuration is challenging, try these:")
    print()
    
    print("🔧 Method 1: Physical Device Reset")
    print("   1. Look for RESET button on device")
    print("   2. Hold while powering on for 10+ seconds")
    print("   3. Device should reset to factory defaults")
    print("   4. Default IP usually: ************* or *************")
    print()
    
    print("🔧 Method 2: Network Scan for Default IP")
    print("   1. Device might be on different IP after reset")
    print("   2. Scan network: 192.168.1.x and 192.168.0.x")
    print("   3. Look for open ports 4370, 80, or 8080")
    print()
    
    print("🔧 Method 3: USB Configuration (if supported)")
    print("   1. Some ZK devices support USB configuration")
    print("   2. Connect via USB cable")
    print("   3. Use ZK software tools")
    print()
    
    print("🔧 Method 4: Contact Device Administrator")
    print("   1. Device may have custom firmware")
    print("   2. May need specific configuration tools")
    print("   3. Check device manual for reset procedures")

def main():
    """Main configuration function"""
    print("🔧 RAW SOCKET ZK DEVICE CONFIGURATION")
    print("=" * 60)
    print("Device: *************")
    print("Testing direct socket communication")
    print("=" * 60)
    
    # Test multiple ports
    working_port = test_multiple_ports()
    
    if working_port:
        print(f"\n🎉 Found working ZK port: {working_port}")
        print(f"You can try using this port in your applications")
    else:
        print(f"\n⚠️ No working ZK protocol ports found")
    
    # Try other methods
    telnet_port = try_telnet_configuration()
    snmp_available = try_snmp_configuration()
    
    if not working_port and not telnet_port and not snmp_available:
        provide_alternative_solutions()
    
    print(f"\n📋 SUMMARY:")
    print(f"   ZK Protocol Port: {working_port or 'Not found'}")
    print(f"   Telnet Port: {telnet_port or 'Not available'}")
    print(f"   SNMP: {'Available' if snmp_available else 'Not available'}")
    
    if working_port:
        print(f"\n🎯 Try updating your scripts to use port {working_port}")
    else:
        print(f"\n🔄 Consider factory reset or contact device administrator")

if __name__ == '__main__':
    main()
