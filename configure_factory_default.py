#!/usr/bin/env python3
"""
Configure Device After Factory Reset
Work with device at factory default IP *************:4370
"""

import requests
import socket
import time
import subprocess
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FactoryDefaultConfig:
    """Configure device at factory default settings"""
    
    def __init__(self):
        self.factory_ip = "*************"
        self.factory_port = 4370
        self.target_ip = "*************"
        self.target_port = 32150
        
    def test_factory_default_access(self):
        """Test access to factory default IP"""
        print("🧪 Testing Factory Default Access")
        print("-" * 40)
        
        # Test network connectivity
        print(f"1. Testing network connectivity to {self.factory_ip}...")
        try:
            result = subprocess.run(["ping", "-n", "2", self.factory_ip], 
                                  capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                print("   ✅ Factory default IP is reachable")
            else:
                print("   ❌ Factory default IP not reachable")
                return False
        except Exception as e:
            print(f"   ❌ Ping failed: {e}")
            return False
        
        # Test port connectivity
        print(f"2. Testing port {self.factory_port}...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.factory_ip, self.factory_port))
            sock.close()
            
            if result == 0:
                print("   ✅ Factory default port is open")
                return True
            else:
                print("   ❌ Factory default port is closed")
                return False
        except Exception as e:
            print(f"   ❌ Port test failed: {e}")
            return False
    
    def test_zk_protocol_factory_default(self):
        """Test ZK protocol on factory default"""
        print("\n🔧 Testing ZK Protocol on Factory Default")
        print("-" * 40)
        
        # Try different authentication combinations
        auth_configs = [
            {"device_id": "1", "description": "Default Device ID 1"},
            {"device_id": "0", "description": "Device ID 0"},
            {"device_id": None, "description": "No Device ID"},
        ]
        
        for config in auth_configs:
            print(f"Testing {config['description']}...")
            
            try:
                from zk_biometric import ZKBiometricDevice
                
                device = ZKBiometricDevice(
                    device_ip=self.factory_ip,
                    port=self.factory_port,
                    timeout=10,
                    device_id=config['device_id'],
                    use_cloud=False
                )
                
                if device.connect():
                    print("   ✅ SUCCESS! ZK protocol working")
                    
                    try:
                        users = device.get_users()
                        print(f"   📱 Found {len(users)} users")
                        
                        # Try to get device info
                        info = device.get_device_info()
                        if info:
                            print(f"   📊 Device info: {info}")
                            
                    except Exception as e:
                        print(f"   ⚠️ Limited access: {e}")
                    
                    device.disconnect()
                    return config['device_id']
                else:
                    print("   ❌ Connection failed")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return None
    
    def configure_via_web_interface(self):
        """Configure device via web interface at factory default IP"""
        print(f"\n🌐 Configuring via Web Interface")
        print("-" * 40)
        
        session = requests.Session()
        session.verify = False
        
        base_url = f"http://{self.factory_ip}"
        
        try:
            # Test web interface access
            print("1. Testing web interface access...")
            response = session.get(base_url, timeout=10)
            
            if response.status_code != 200:
                print(f"   ❌ Web interface not accessible: HTTP {response.status_code}")
                return False
            
            print("   ✅ Web interface accessible")
            
            # Try to login
            print("2. Attempting login...")
            login_data = {
                "username": "admin",
                "password": "123456",
                "user": "admin",
                "pass": "123456"
            }
            
            login_response = session.post(base_url, data=login_data, timeout=10)
            
            if login_response.status_code == 200:
                print("   ✅ Login successful")
                
                # Look for network configuration
                print("3. Looking for network configuration...")
                
                config_paths = [
                    "/network.html",
                    "/system.html",
                    "/comm.html",
                    "/config.html"
                ]
                
                for path in config_paths:
                    try:
                        config_url = base_url + path
                        config_response = session.get(config_url, timeout=5)
                        
                        if config_response.status_code == 200:
                            print(f"   ✅ Found configuration page: {path}")
                            
                            # Try to configure network settings
                            network_data = {
                                "ip": self.target_ip,
                                "device_id": "181",
                                "common_key": "1302",
                                "port": str(self.target_port),
                                "tcp_enable": "1",
                                "submit": "Save"
                            }
                            
                            config_submit = session.post(config_url, data=network_data, timeout=10)
                            
                            if config_submit.status_code == 200:
                                print(f"   ✅ Configuration submitted successfully")
                                return True
                            
                    except Exception as e:
                        continue
                
                print("   ⚠️ No configuration page found")
                return False
            else:
                print("   ❌ Login failed")
                return False
                
        except Exception as e:
            print(f"   ❌ Web configuration failed: {e}")
            return False
    
    def manual_configuration_guide(self):
        """Provide manual configuration guide for factory default"""
        print(f"\n📋 MANUAL CONFIGURATION GUIDE")
        print("=" * 50)
        
        print("Since the device is now at factory defaults, follow these steps:")
        print()
        
        print("🌐 WEB INTERFACE METHOD:")
        print(f"1. Open browser and go to: http://{self.factory_ip}")
        print("2. Login with:")
        print("   - Username: admin")
        print("   - Password: 123456")
        print("3. Look for Network/Communication settings")
        print("4. Configure:")
        print(f"   - IP Address: {self.target_ip}")
        print("   - Device ID: 181")
        print("   - Common Key: 1302")
        print(f"   - Port: {self.target_port}")
        print("   - TCP/IP: Enable")
        print("5. Save and restart device")
        print()
        
        print("🔧 DIRECT ZK PROTOCOL METHOD:")
        print("If ZK protocol is working, you can:")
        print("1. Connect to device at factory defaults")
        print("2. Use ZK software to reconfigure network settings")
        print("3. Set target IP and communication parameters")
        print()
        
        print("📱 DEVICE MENU METHOD:")
        print("If device has display/keypad:")
        print("1. Access device menu")
        print("2. Navigate to Network/Communication settings")
        print("3. Configure IP, Device ID, and Common Key")
        print("4. Save settings and restart")
    
    def test_final_configuration(self):
        """Test if device is working at target configuration"""
        print(f"\n🧪 Testing Final Configuration")
        print("-" * 40)
        
        print(f"Testing connection to {self.target_ip}:{self.target_port}...")
        
        try:
            from zk_biometric import ZKBiometricDevice
            
            device = ZKBiometricDevice(
                device_ip=self.target_ip,
                port=self.target_port,
                timeout=15,
                device_id='181',
                use_cloud=False
            )
            
            if device.connect():
                print("✅ SUCCESS! Device configured and working at target settings!")
                
                try:
                    users = device.get_users()
                    print(f"📱 Found {len(users)} users")
                    
                    records = device.get_attendance_records()
                    print(f"📊 Found {len(records)} attendance records")
                    
                except Exception as e:
                    print(f"⚠️ Connected but limited access: {e}")
                
                device.disconnect()
                return True
            else:
                print("❌ Device not yet configured at target settings")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False

def main():
    """Main factory default configuration"""
    print("🔧 FACTORY DEFAULT DEVICE CONFIGURATION")
    print("=" * 60)
    print("Factory Default: *************:4370")
    print("Target Settings: *************:32150, Device ID: 181")
    print("=" * 60)
    
    config = FactoryDefaultConfig()
    
    # Step 1: Test factory default access
    if not config.test_factory_default_access():
        print("\n❌ Cannot access device at factory defaults")
        print("Try manual hardware reset or check network connection")
        return
    
    # Step 2: Test ZK protocol
    working_device_id = config.test_zk_protocol_factory_default()
    
    if working_device_id is not None:
        print(f"\n✅ ZK protocol working with Device ID: {working_device_id}")
        print("You can now configure the device programmatically or via web interface")
    else:
        print(f"\n⚠️ ZK protocol not working, but device is accessible")
    
    # Step 3: Try web interface configuration
    if config.configure_via_web_interface():
        print(f"\n✅ Web interface configuration completed")
        
        # Wait for device to apply settings
        print("⏳ Waiting 30 seconds for device to apply settings...")
        time.sleep(30)
        
        # Test final configuration
        if config.test_final_configuration():
            print(f"\n🎉 CONFIGURATION COMPLETE!")
            print("Device is now working at target settings")
            return
    
    # Step 4: Provide manual guide
    config.manual_configuration_guide()
    
    print(f"\n📋 SUMMARY:")
    print(f"✅ Device accessible at factory defaults: {config.factory_ip}:{config.factory_port}")
    print(f"⚙️ Manual configuration needed to reach target: {config.target_ip}:{config.target_port}")
    print(f"🌐 Web interface: http://{config.factory_ip}")

if __name__ == '__main__':
    main()
